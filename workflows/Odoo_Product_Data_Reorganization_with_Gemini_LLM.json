{"nodes": [{"parameters": {"rule": {"interval": [{"field": "hours", "hoursInterval": 24}]}}, "id": "e1ea1195-2254-4a8a-9445-41a184e260c8", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [-1840, -300], "typeVersion": 1.2}, {"parameters": {"resource": "custom", "customResource": "product.template", "operation": "getAll", "limit": 50, "options": {"fields": "id,name,list_price,standard_price,sale_ok,default_code,type,categ_id,public_categ_ids,pos_categ_ids,description,attribute_line_ids,image_1920,product_template_attribute_value_ids,external_id,website_url,property_account_income_id,property_account_expense_id,available_in_pos"}}, "id": "d3f707ac-9d36-4605-869e-fbf6e5a27aa1", "name": "Get Products from Odoo", "type": "n8n-nodes-base.odoo", "position": [-1620, -400], "typeVersion": 1, "credentials": {"odooApi": {"id": "tLv6s096EQSwE4vP", "name": "Odoo account"}}}, {"parameters": {"resource": "custom", "customResource": "product.category", "operation": "getAll", "options": {}}, "id": "c303c681-1b6d-4030-b1e3-1de428267199", "name": "Get Categories from Odoo", "type": "n8n-nodes-base.odoo", "position": [-1620, -200], "typeVersion": 1, "credentials": {"odooApi": {"id": "tLv6s096EQSwE4vP", "name": "Odoo account"}}}, {"parameters": {"resource": "custom", "customResource": "product.attribute", "operation": "getAll", "options": {}}, "id": "731ee032-a03d-402f-8015-1a569893c84b", "name": "Get Attributes from Odoo", "type": "n8n-nodes-base.odoo", "position": [-1620, 20], "typeVersion": 1, "credentials": {"odooApi": {"id": "tLv6s096EQSwE4vP", "name": "Odoo account"}}}, {"parameters": {"resource": "custom", "customResource": "product.public.category", "operation": "getAll", "options": {}}, "id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890", "name": "Get Website Categories from Odoo", "type": "n8n-nodes-base.odoo", "position": [-1620, 120], "typeVersion": 1, "credentials": {"odooApi": {"id": "tLv6s096EQSwE4vP", "name": "Odoo account"}}}, {"parameters": {"resource": "custom", "customResource": "pos.category", "operation": "getAll", "options": {}}, "id": "b2c3d4e5-f6g7-8901-bcde-f23456789012", "name": "Get POS Categories from Odoo", "type": "n8n-nodes-base.odoo", "position": [-1620, 320], "typeVersion": 1, "credentials": {"odooApi": {"id": "tLv6s096EQSwE4vP", "name": "Odoo account"}}}, {"parameters": {"documentId": {"__rl": true, "value": "1BSAa8scQzL7apV--Eg13ZtRLEni6zlNbSVLm_bwSz2A", "mode": "list", "cachedResultName": "Ceratech Categories", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1BSAa8scQzL7apV--Eg13ZtRLEni6zlNbSVLm_bwSz2A/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "categories", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1BSAa8scQzL7apV--Eg13ZtRLEni6zlNbSVLm_bwSz2A/edit#gid=0"}, "options": {}}, "id": "ec1fdd15-c45a-426f-9a83-2c5e0d73fcca", "name": "Read Categories Mapping", "type": "n8n-nodes-base.googleSheets", "position": [-1620, 220], "typeVersion": 4, "credentials": {"googleSheetsOAuth2Api": {"id": "dZ7tkqELmygzeIYp", "name": "Google Sheets account"}}}, {"parameters": {"documentId": "your_reorganization_document_id", "sheetName": "re-organization", "options": {}}, "id": "d0849b49-5c1d-41ae-8289-9a78ef406dd8", "name": "Read Processed Products", "type": "n8n-nodes-base.googleSheets", "position": [-1400, 220], "typeVersion": 4, "credentials": {"googleSheetsOAuth2Api": {"id": "dZ7tkqELmygzeIYp", "name": "Google Sheets account"}}}, {"parameters": {"mode": "combine", "mergeByFields": {"values": [{}]}, "options": {}}, "id": "140e76c7-3b5e-4502-bdce-a90c5b9c45fe", "name": "Merge Products & Categories", "type": "n8n-nodes-base.merge", "position": [-1400, -300], "typeVersion": 2.1}, {"parameters": {"mode": "combine", "mergeByFields": {"values": [{}]}, "options": {}}, "id": "47980af1-8851-4827-b89e-2b71d8f6322c", "name": "Merge Attributes & Mapping", "type": "n8n-nodes-base.merge", "position": [-1400, -100], "typeVersion": 2.1}, {"parameters": {"mode": "combine", "mergeByFields": {"values": [{}]}, "options": {}}, "id": "6b87d612-c48f-4d38-b7fa-8a26fd264709", "name": "Merge All Data", "type": "n8n-nodes-base.merge", "position": [-1180, -200], "typeVersion": 2.1}, {"parameters": {"operation": "create", "documentId": "your_document_id", "options": {}}, "id": "aa5c4077-fb97-41cf-80ee-5ab9dbcb7991", "name": "Create Backup", "type": "n8n-nodes-base.googleSheets", "position": [-1180, 20], "typeVersion": 4, "credentials": {"googleSheetsOAuth2Api": {"id": "dZ7tkqELmygzeIYp", "name": "Google Sheets account"}}}, {"parameters": {"jsCode": "// Structure merged data properly\nconst allData = $input.all();\n\n// Extract data from different merge operations\nconst mergedProductsCategories = allData[0] || {};\nconst mergedAttributesMapping = allData[1] || {};\nconst processedProducts = allData[2] || {};\n\n// Structure the data\nconst structuredData = {\n  products: mergedProductsCategories.json || [],\n  productCategories: mergedProductsCategories.json1 || [],\n  websiteCategories: mergedProductsCategories.json2 || [],\n  posCategories: mergedProductsCategories.json3 || [],\n  attributes: mergedAttributesMapping.json || [],\n  mapping: mergedAttributesMapping.json1 || [],\n  processedProducts: processedProducts.json || []\n};\n\nreturn { json: structuredData };"}, "id": "c1d2e3f4-g5h6-7890-ijkl-mn1234567890", "name": "Structure Merged Data", "type": "n8n-nodes-base.code", "position": [-1180, -200], "typeVersion": 2}, {"parameters": {"jsCode": "// Extract data from structured input\nconst data = $json;\nconst products = data.products || [];\nconst productCategories = data.productCategories || [];\nconst websiteCategories = data.websiteCategories || [];\nconst posCategories = data.posCategories || [];\nconst attributes = data.attributes || [];\nconst mapping = data.mapping || [];\nconst processedProducts = data.processedProducts || [];\n\n// Create a set of already processed product IDs\nconst processedIds = new Set(processedProducts.map(p => p.id));\n\n// Filter out already processed products\nconst unprocessedProducts = products.filter(product => !processedIds.has(product.id));\n\n// Reduce batch size to 3 to avoid LLM token overflow with all the new fields\nconst batchSize = 3;\nconst batches = [];\nfor (let i = 0; i < unprocessedProducts.length; i += batchSize) {\n  const batch = {\n    products: unprocessedProducts.slice(i, i + batchSize),\n    productCategories: productCategories,\n    websiteCategories: websiteCategories,\n    posCategories: posCategories,\n    attributes: attributes,\n    mapping: mapping,\n    batchNumber: Math.floor(i / batchSize) + 1,\n    totalBatches: Math.ceil(unprocessedProducts.length / batchSize)\n  };\n  batches.push(batch);\n}\n\nreturn batches.map(batch => ({ json: batch }));"}, "id": "33e3eadc-6dbf-4904-be48-76c146c871cf", "name": "Filter and Batch Products", "type": "n8n-nodes-base.code", "position": [-960, -200], "typeVersion": 2}, {"parameters": {"mode": "runOnceForEachItem", "jsCode": "const batch = $json;\nconst prompt = `You are an expert product data analyst for Odoo ERP. Analyze and reorganize product data with proper categorization across all three category types.\n\nTASK: Reorganize product categories, merge variants, fix inconsistencies, and ensure proper categorization.\n\nPRODUCTS TO ANALYZE (Batch ${batch.batchNumber}/${batch.totalBatches}):\n${JSON.stringify(batch.products, null, 2)}\n\nPRODUCT CATEGORIES (categ_id):\n${JSON.stringify(batch.productCategories, null, 2)}\n\nWEBSITE CATEGORIES (public_categ_ids):\n${JSON.stringify(batch.websiteCategories, null, 2)}\n\nPOS CATEGORIES (pos_categ_ids):\n${JSON.stringify(batch.posCategories, null, 2)}\n\nCATEGORY MAPPINGS:\n${JSON.stringify(batch.mapping, null, 2)}\n\nATTRIBUTES:\n${JSON.stringify(batch.attributes, null, 2)}\n\nINSTRUCTIONS:\n1. Analyze each product for proper categorization across all three category types\n2. Use category mappings to assign correct categories\n3. Identify and merge potential product variants\n4. Fix data inconsistencies and improve product information\n5. Ensure all required fields are properly filled\n6. Return ONLY valid JSON with reorganized products\n\nRETURN FORMAT:\n{\n  \"reorganized_products\": [\n    {\n      \"id\": \"product_id\",\n      \"name\": \"updated_name\",\n      \"list_price\": \"price\",\n      \"standard_price\": \"cost\",\n      \"sale_ok\": true/false,\n      \"default_code\": \"internal_reference\",\n      \"type\": \"product_type\",\n      \"categ_id\": \"product_category_id\",\n      \"public_categ_ids\": [\"website_category_ids\"],\n      \"pos_categ_ids\": [\"pos_category_ids\"],\n      \"description\": \"description\",\n      \"attribute_line_ids\": [\"attribute_ids\"],\n      \"external_id\": \"external_id\",\n      \"website_url\": \"url\",\n      \"property_account_income_id\": \"income_account\",\n      \"property_account_expense_id\": \"expense_account\",\n      \"available_in_pos\": true/false,\n      \"changes_made\": \"description of changes\",\n      \"merged_variants\": [\"list of merged product ids if any\"]\n    }\n  ]\n}`;\n\nreturn { json: { prompt: prompt, batchData: batch } };"}, "id": "d4420562-e91b-42b2-b59f-d108fdbcf977", "name": "Prepare Gemini Prompt", "type": "n8n-nodes-base.code", "position": [-740, -200], "typeVersion": 2}, {"parameters": {}, "id": "f6658c2e-faea-4ab5-9758-3df2b9d2604d", "name": "Basic LLM Chain", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [-520, -200], "typeVersion": 1.4}, {"parameters": {"modelName": "models/gemini-2.5-pro", "options": {}}, "id": "5db4f4b0-b5b7-449d-abf7-4f38b41f6cd7", "name": "Google Gemini Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [-520, -40], "typeVersion": 1, "credentials": {"googlePalmApi": {"id": "APRdhk8GlK6SwVjG", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {"mode": "runOnceForEachItem", "jsCode": "let responseText = $json.response || $json.text || '';\n\n// Try to extract <PERSON><PERSON><PERSON> from the response\nlet parsedData;\ntry {\n  // First try direct parsing\n  parsedData = JSON.parse(responseText);\n} catch (e) {\n  try {\n    // Try to find <PERSON><PERSON><PERSON> within the response\n    const jsonMatch = responseText.match(/\\{[\\s\\S]*\\}/);\n    if (jsonMatch) {\n      parsedData = JSON.parse(jsonMatch[0]);\n    } else {\n      throw new Error('No JSON found in response');\n    }\n  } catch (e2) {\n    // Fallback: create a basic structure\n    console.log('Failed to parse Gemini response:', responseText);\n    parsedData = {\n      reorganized_products: [],\n      error: 'Failed to parse LLM response',\n      original_response: responseText\n    };\n  }\n}\n\n// Validate the structure\nif (!parsedData.reorganized_products || !Array.isArray(parsedData.reorganized_products)) {\n  parsedData = {\n    reorganized_products: [],\n    error: 'Invalid response structure',\n    original_response: responseText\n  };\n}\n\nreturn { json: { ...parsedData, batchData: $('Prepare Gemini Prompt').item.json.batchData } };"}, "id": "db7f0469-9548-4c38-81a3-ef326c9d0ed8", "name": "Parse Gemini Response", "type": "n8n-nodes-base.code", "position": [-160, -200], "typeVersion": 2}, {"parameters": {"options": {}}, "id": "a7b335a5-638f-47d2-bd60-9c94905e9f45", "name": "Split Products", "type": "n8n-nodes-base.splitInBatches", "position": [60, -200], "typeVersion": 3}, {"parameters": {"operation": "append", "documentId": {"__rl": true, "value": "1p223ptpK8dk6UNuHhK4xwXC4BzBC_nycsFQ-IAKDlKc", "mode": "list", "cachedResultName": "re-organization", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1p223ptpK8dk6UNuHhK4xwXC4BzBC_nycsFQ-IAKDlKc/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "product_list", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1p223ptpK8dk6UNuHhK4xwXC4BzBC_nycsFQ-IAKDlKc/edit#gid=0"}, "columns": {"mappingMode": "defineBelow", "value": {"Nom": "={{ $json.name }}", "Prix de vente": "={{ $json.list_price }}", "Coût": "={{ $json.standard_price }}", "Peut être vendu": "={{ $json.sale_ok }}", "Référence interne": "={{ $json.default_code }}", "Type": "={{ $json.type }}", "Catégorie de produits": "={{ $json.categ_id }}", "Catégorie de produits du site web/ID externe": "={{ $json.public_categ_ids ? $json.public_categ_ids.join(',') : '' }}", "Catégorie du point de vente/ID externe": "={{ $json.pos_categ_ids ? $json.pos_categ_ids.join(',') : '' }}", "Description": "={{ $json.description }}", "Attributs de produits/Attribut": "={{ $json.attribute_line_ids ? $json.attribute_line_ids.join(',') : '' }}", "Attributs de produits/Valeurs": "={{ $json.product_template_attribute_value_ids ? $json.product_template_attribute_value_ids.join(',') : '' }}", "ID externe": "={{ $json.external_id }}", "Image": "={{ $json.image_1920 ? 'Yes' : 'No' }}", "Médias supplémentaires pour le produit/Image": "", "Médias supplémentaires pour le produit/Nom": "", "Url": "={{ $json.website_url }}", "Compte des revenus": "={{ $json.property_account_income_id }}", "Compte de charges": "={{ $json.property_account_expense_id }}", "Disponible dans le PdV": "={{ $json.available_in_pos }}", "Modifications": "={{ $json.changes_made }}", "Date de traitement": "={{ $now.format('yyyy-MM-dd HH:mm:ss') }}"}}, "options": {}}, "id": "502e01d6-846b-4e52-bfb6-30d1f278256b", "name": "Append to Re-organization Sheet", "type": "n8n-nodes-base.googleSheets", "position": [460, -220], "typeVersion": 4, "credentials": {"googleSheetsOAuth2Api": {"id": "dZ7tkqELmygzeIYp", "name": "Google Sheets account"}}}, {"parameters": {"resource": "custom", "customResource": "product.template", "operation": "update", "id": "={{ $json.id }}", "updateFields": {"name": "={{ $json.name }}", "list_price": "={{ $json.list_price }}", "standard_price": "={{ $json.standard_price }}", "sale_ok": "={{ $json.sale_ok }}", "default_code": "={{ $json.default_code }}", "type": "={{ $json.type }}", "categ_id": "={{ $json.categ_id }}", "public_categ_ids": "={{ $json.public_categ_ids }}", "pos_categ_ids": "={{ $json.pos_categ_ids }}", "description": "={{ $json.description }}", "available_in_pos": "={{ $json.available_in_pos }}", "website_url": "={{ $json.website_url }}"}}, "id": "de3deeae-3dc9-47b7-b254-48e3184f9637", "name": "Update Products in Odoo", "type": "n8n-nodes-base.odoo", "position": [860, -220], "typeVersion": 1, "credentials": {"odooApi": {"id": "tLv6s096EQSwE4vP", "name": "Odoo account"}}}, {"parameters": {"operation": "create"}, "id": "fdf1e297-3e55-4ba0-a439-1e54c2a7ced3", "name": "Send Notification", "type": "n8n-nodes-base.slack", "position": [1080, -220], "typeVersion": 2, "webhookId": "c192927f-54bd-4174-9521-662d43300850", "credentials": {}}], "connections": {"Schedule Trigger": {"main": [[{"node": "Get Products from Odoo", "type": "main", "index": 0}, {"node": "Get Categories from Odoo", "type": "main", "index": 0}, {"node": "Get Attributes from Odoo", "type": "main", "index": 0}, {"node": "Get Website Categories from Odoo", "type": "main", "index": 0}, {"node": "Get POS Categories from Odoo", "type": "main", "index": 0}, {"node": "Read Categories Mapping", "type": "main", "index": 0}, {"node": "Read Processed Products", "type": "main", "index": 0}]]}, "Get Products from Odoo": {"main": [[{"node": "Merge Products & Categories", "type": "main", "index": 0}]]}, "Get Categories from Odoo": {"main": [[{"node": "Merge Products & Categories", "type": "main", "index": 1}]]}, "Get Attributes from Odoo": {"main": [[{"node": "Merge Attributes & Mapping", "type": "main", "index": 0}]]}, "Get Website Categories from Odoo": {"main": [[{"node": "Merge Products & Categories", "type": "main", "index": 2}]]}, "Get POS Categories from Odoo": {"main": [[{"node": "Merge Products & Categories", "type": "main", "index": 3}]]}, "Read Categories Mapping": {"main": [[{"node": "Merge Attributes & Mapping", "type": "main", "index": 1}]]}, "Merge Products & Categories": {"main": [[{"node": "Merge All Data", "type": "main", "index": 0}]]}, "Merge Attributes & Mapping": {"main": [[{"node": "Merge All Data", "type": "main", "index": 1}]]}, "Merge All Data": {"main": [[{"node": "Structure Merged Data", "type": "main", "index": 0}, {"node": "Create Backup", "type": "main", "index": 0}]]}, "Structure Merged Data": {"main": [[{"node": "Filter and Batch Products", "type": "main", "index": 0}]]}, "Filter and Batch Products": {"main": [[{"node": "Prepare Gemini Prompt", "type": "main", "index": 0}]]}, "Prepare Gemini Prompt": {"main": [[{"node": "Basic LLM Chain", "type": "main", "index": 0}]]}, "Basic LLM Chain": {"main": [[{"node": "Parse Gemini Response", "type": "main", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "Basic LLM Chain", "type": "ai_languageModel", "index": 0}]]}, "Parse Gemini Response": {"main": [[{"node": "Split Products", "type": "main", "index": 0}]]}, "Split Products": {"main": [[{"node": "Append to Re-organization Sheet", "type": "main", "index": 0}]]}, "Append to Re-organization Sheet": {"main": [[{"node": "Update Products in Odoo", "type": "main", "index": 0}]]}, "Update Products in Odoo": {"main": [[{"node": "Send Notification", "type": "main", "index": 0}]]}, "Read Processed Products": {"main": [[{"node": "Structure Merged Data", "type": "main", "index": 2}]]}}, "pinData": {}, "meta": {"templateCredsSetupCompleted": true, "instanceId": "279040e9cdb85e8ba06a771d7e2f12db781b73ea8768bfce2d2787e09f554bea"}}