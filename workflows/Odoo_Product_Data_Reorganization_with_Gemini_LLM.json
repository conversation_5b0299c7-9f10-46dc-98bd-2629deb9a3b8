{"id": "OdooProductReorganization2024", "meta": {"instanceId": "odoo-product-reorganization-gemini-llm", "templateCredsSetupCompleted": true}, "name": "Odoo Product Data Reorganization with Google Gemini LLM", "tags": [], "active": true, "nodes": [{"id": "schedule-trigger", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [240, 300], "parameters": {"rule": {"interval": [{"field": "hours", "hoursInterval": 24}]}}, "typeVersion": 1.2}, {"id": "get-products-odoo", "name": "Get Products from Odoo", "type": "n8n-nodes-base.odoo", "position": [460, 200], "parameters": {"resource": "product", "operation": "getAll", "limit": 100, "returnAll": false}, "credentials": {"odooApi": {"id": "odoo-credentials", "name": "Odoo Credentials"}}, "typeVersion": 1}, {"id": "get-categories-odoo", "name": "Get Categories from Odoo", "type": "n8n-nodes-base.odoo", "position": [460, 400], "parameters": {"resource": "productCategory", "operation": "getAll", "returnAll": true}, "credentials": {"odooApi": {"id": "odoo-credentials", "name": "Odoo Credentials"}}, "typeVersion": 1}, {"id": "get-attributes-odoo", "name": "Get Attributes from Odoo", "type": "n8n-nodes-base.odoo", "position": [460, 600], "parameters": {"resource": "productAttribute", "operation": "getAll", "returnAll": true}, "credentials": {"odooApi": {"id": "odoo-credentials", "name": "Odoo Credentials"}}, "typeVersion": 1}, {"id": "read-categories-mapping", "name": "Read Categories Mapping", "type": "n8n-nodes-base.googleSheets", "position": [460, 800], "parameters": {"operation": "read", "documentId": "your_document_id", "sheetName": "Categories Mapping", "options": {}}, "credentials": {"googleSheetsOAuth2Api": {"id": "google-sheets-account", "name": "Google Sheets account"}}, "typeVersion": 4}, {"id": "read-processed-products", "name": "Read Processed Products", "type": "n8n-nodes-base.googleSheets", "position": [680, 800], "parameters": {"operation": "read", "documentId": "your_reorganization_document_id", "sheetName": "re-organization", "options": {}}, "credentials": {"googleSheetsOAuth2Api": {"id": "google-sheets-account", "name": "Google Sheets account"}}, "typeVersion": 4}, {"id": "merge-products-categories", "name": "Merge Products & Categories", "type": "n8n-nodes-base.merge", "position": [680, 300], "parameters": {"mode": "combine", "combineBy": "combineByPosition"}, "typeVersion": 2.1}, {"id": "merge-attributes-mapping", "name": "Merge Attributes & Mapping", "type": "n8n-nodes-base.merge", "position": [680, 500], "parameters": {"mode": "combine", "combineBy": "combineByPosition"}, "typeVersion": 2.1}, {"id": "merge-all-data", "name": "Merge All Data", "type": "n8n-nodes-base.merge", "position": [900, 400], "parameters": {"mode": "combine", "combineBy": "combineByPosition"}, "typeVersion": 2.1}, {"id": "create-backup", "name": "Create Backup", "type": "n8n-nodes-base.googleSheets", "position": [900, 600], "parameters": {"operation": "create", "documentId": "your_document_id", "sheetName": "={{ 'Backup_' + $now.format('yyyy-MM-dd') }}", "options": {}}, "credentials": {"googleSheetsOAuth2Api": {"id": "google-sheets-account", "name": "Google Sheets account"}}, "typeVersion": 4}, {"id": "filter-and-batch-products", "name": "Filter and Batch Products", "type": "n8n-nodes-base.code", "position": [1120, 400], "parameters": {"mode": "runOnceForAllItems", "jsCode": "// Extract data from merged inputs\nconst allData = $input.all();\nconst products = allData[0]?.json?.products || [];\nconst categories = allData[0]?.json?.categories || [];\nconst attributes = allData[0]?.json?.attributes || [];\nconst mapping = allData[0]?.json?.mapping || [];\nconst processedProducts = $input.item(1)?.json?.processedProducts || [];\n\n// Create a set of already processed product IDs\nconst processedIds = new Set(processedProducts.map(p => p.id));\n\n// Filter out already processed products\nconst unprocessedProducts = products.filter(product => !processedIds.has(product.id));\n\n// Split products into batches of 10\nconst batchSize = 10;\nconst batches = [];\nfor (let i = 0; i < unprocessedProducts.length; i += batchSize) {\n  const batch = {\n    products: unprocessedProducts.slice(i, i + batchSize),\n    categories: categories,\n    attributes: attributes,\n    mapping: mapping,\n    batchNumber: Math.floor(i / batchSize) + 1,\n    totalBatches: Math.ceil(unprocessedProducts.length / batchSize)\n  };\n  batches.push(batch);\n}\n\nreturn batches.map(batch => ({ json: batch }));"}, "typeVersion": 2}, {"id": "prepare-gemini-prompt", "name": "Prepare Gemini Prompt", "type": "n8n-nodes-base.code", "position": [1340, 400], "parameters": {"mode": "runOnceForEachItem", "jsCode": "const batch = $json;\nconst prompt = `You are an expert product data analyst. Analyze the following product data and reorganize it according to the category mappings provided.\n\nTASK: Reorganize product categories, merge potential variants, and fix inconsistencies.\n\nPRODUCTS TO ANALYZE (Batch ${batch.batchNumber}/${batch.totalBatches}):\n${JSON.stringify(batch.products, null, 2)}\n\nAVAILABLE CATEGORIES:\n${JSON.stringify(batch.categories, null, 2)}\n\nCATEGORY MAPPINGS:\n${JSON.stringify(batch.mapping, null, 2)}\n\nATTRIBUTES:\n${JSON.stringify(batch.attributes, null, 2)}\n\nINSTRUCTIONS:\n1. Analyze each product for proper categorization\n2. Identify potential product variants that should be merged\n3. Fix category inconsistencies using the mapping\n4. Ensure data quality and consistency\n5. Return ONLY valid JSON with the reorganized products\n\nRETURN FORMAT:\n{\n  \"reorganized_products\": [\n    {\n      \"id\": \"product_id\",\n      \"name\": \"updated_name\",\n      \"category_id\": \"new_category_id\",\n      \"list_price\": \"price\",\n      \"standard_price\": \"cost\",\n      \"description\": \"description\",\n      \"changes_made\": \"description of changes\",\n      \"merged_variants\": [\"list of merged product ids if any\"]\n    }\n  ]\n}`;\n\nreturn { json: { prompt: prompt, batchData: batch } };"}, "typeVersion": 2}, {"id": "basic-llm-chain", "name": "Basic LLM Chain", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [1560, 400], "parameters": {"prompt": "={{ $json.prompt }}"}, "typeVersion": 1.4}, {"id": "google-gemini-chat-model", "name": "Google Gemini Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [1560, 560], "parameters": {"options": {}}, "credentials": {"googlePalmApi": {"id": "google-gemini-account", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}, {"id": "parse-gemini-response", "name": "Parse Gemini Response", "type": "n8n-nodes-base.code", "position": [1780, 400], "parameters": {"mode": "runOnceForEachItem", "jsCode": "let responseText = $json.response || $json.text || '';\n\n// Try to extract <PERSON><PERSON><PERSON> from the response\nlet parsedData;\ntry {\n  // First try direct parsing\n  parsedData = JSON.parse(responseText);\n} catch (e) {\n  try {\n    // Try to find <PERSON><PERSON><PERSON> within the response\n    const jsonMatch = responseText.match(/\\{[\\s\\S]*\\}/);\n    if (jsonMatch) {\n      parsedData = JSON.parse(jsonMatch[0]);\n    } else {\n      throw new Error('No JSON found in response');\n    }\n  } catch (e2) {\n    // Fallback: create a basic structure\n    console.log('Failed to parse Gemini response:', responseText);\n    parsedData = {\n      reorganized_products: [],\n      error: 'Failed to parse LLM response',\n      original_response: responseText\n    };\n  }\n}\n\n// Validate the structure\nif (!parsedData.reorganized_products || !Array.isArray(parsedData.reorganized_products)) {\n  parsedData = {\n    reorganized_products: [],\n    error: 'Invalid response structure',\n    original_response: responseText\n  };\n}\n\nreturn { json: { ...parsedData, batchData: $('Prepare Gemini Prompt').item.json.batchData } };"}, "typeVersion": 2}, {"id": "split-products", "name": "Split Products", "type": "n8n-nodes-base.splitInBatches", "position": [2000, 400], "parameters": {"mode": "splitInOut", "splitBy": "list", "batchSize": 1, "list": "={{ $json.reorganized_products }}"}, "typeVersion": 3}, {"id": "append-to-reorganization-sheet", "name": "Append to Re-organization Sheet", "type": "n8n-nodes-base.googleSheets", "position": [2220, 400], "parameters": {"operation": "append", "documentId": "your_reorganization_document_id", "sheetName": "re-organization", "options": {}, "values": {"Nom": "={{ $json.name }}", "Prix de vente": "={{ $json.list_price }}", "Coût": "={{ $json.standard_price }}", "Catégorie": "={{ $json.category_id }}", "Description": "={{ $json.description }}", "Modifications": "={{ $json.changes_made }}", "Variantes fusionnées": "={{ $json.merged_variants ? $json.merged_variants.join(', ') : '' }}", "Date de traitement": "={{ $now.format('yyyy-MM-dd HH:mm:ss') }}", "ID Produit": "={{ $json.id }}"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "google-sheets-account", "name": "Google Sheets account"}}, "typeVersion": 4}, {"id": "update-products-in-odoo", "name": "Update Products in Odoo", "type": "n8n-nodes-base.odoo", "position": [2440, 400], "parameters": {"resource": "product", "operation": "update", "id": "={{ $json.id }}", "updateFields": {"name": "={{ $json.name }}", "list_price": "={{ $json.list_price }}", "standard_price": "={{ $json.standard_price }}", "categ_id": "={{ $json.category_id }}", "description": "={{ $json.description }}"}}, "credentials": {"odooApi": {"id": "odoo-credentials", "name": "Odoo Credentials"}}, "typeVersion": 1}, {"id": "send-notification", "name": "Send Notification", "type": "n8n-nodes-base.slack", "position": [2660, 400], "parameters": {"operation": "create", "channel": "#odoo-automation", "text": "✅ Odoo Product Reorganization Completed\\n\\nProcessed: {{ $('Split Products').item.json.name }}\\nChanges: {{ $('Split Products').item.json.changes_made }}\\nTime: {{ $now.format('yyyy-MM-dd HH:mm:ss') }}"}, "credentials": {"slackApi": {"id": "slack-credentials", "name": "Slack Credentials"}}, "typeVersion": 2}], "connections": {"Schedule Trigger": {"main": [[{"node": "Get Products from Odoo", "type": "main", "index": 0}, {"node": "Get Categories from Odoo", "type": "main", "index": 0}, {"node": "Get Attributes from Odoo", "type": "main", "index": 0}, {"node": "Read Categories Mapping", "type": "main", "index": 0}, {"node": "Read Processed Products", "type": "main", "index": 0}]]}, "Get Products from Odoo": {"main": [[{"node": "Merge Products & Categories", "type": "main", "index": 0}]]}, "Get Categories from Odoo": {"main": [[{"node": "Merge Products & Categories", "type": "main", "index": 1}]]}, "Get Attributes from Odoo": {"main": [[{"node": "Merge Attributes & Mapping", "type": "main", "index": 0}]]}, "Read Categories Mapping": {"main": [[{"node": "Merge Attributes & Mapping", "type": "main", "index": 1}]]}, "Merge Products & Categories": {"main": [[{"node": "Merge All Data", "type": "main", "index": 0}]]}, "Merge Attributes & Mapping": {"main": [[{"node": "Merge All Data", "type": "main", "index": 1}]]}, "Merge All Data": {"main": [[{"node": "Filter and Batch Products", "type": "main", "index": 0}, {"node": "Create Backup", "type": "main", "index": 0}]]}, "Read Processed Products": {"main": [[{"node": "Filter and Batch Products", "type": "main", "index": 1}]]}, "Filter and Batch Products": {"main": [[{"node": "Prepare Gemini Prompt", "type": "main", "index": 0}]]}, "Prepare Gemini Prompt": {"main": [[{"node": "Basic LLM Chain", "type": "main", "index": 0}]]}, "Basic LLM Chain": {"main": [[{"node": "Parse Gemini Response", "type": "main", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "Basic LLM Chain", "type": "ai_languageModel", "index": 0}]]}, "Parse Gemini Response": {"main": [[{"node": "Split Products", "type": "main", "index": 0}]]}, "Split Products": {"main": [[{"node": "Append to Re-organization Sheet", "type": "main", "index": 0}]]}, "Append to Re-organization Sheet": {"main": [[{"node": "Update Products in Odoo", "type": "main", "index": 0}]]}, "Update Products in Odoo": {"main": [[{"node": "Send Notification", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "triggerCount": 0, "updatedAt": "2024-08-04T00:00:00.000Z", "versionId": "1"}