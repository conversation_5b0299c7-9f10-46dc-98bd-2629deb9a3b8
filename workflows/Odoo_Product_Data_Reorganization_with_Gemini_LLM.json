{"nodes": [{"parameters": {"rule": {"interval": [{"field": "hours", "hoursInterval": 24}]}}, "id": "e1ea1195-2254-4a8a-9445-41a184e260c8", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [-1840, -300], "typeVersion": 1.2}, {"parameters": {"resource": "custom", "customResource": "product.template", "operation": "getAll", "limit": 20, "options": {}}, "id": "d3f707ac-9d36-4605-869e-fbf6e5a27aa1", "name": "Get Products from Odoo", "type": "n8n-nodes-base.odoo", "position": [-1620, -400], "typeVersion": 1, "credentials": {"odooApi": {"id": "tLv6s096EQSwE4vP", "name": "Odoo account"}}}, {"parameters": {"resource": "custom", "customResource": "product.category", "operation": "getAll", "options": {}}, "id": "c303c681-1b6d-4030-b1e3-1de428267199", "name": "Get Categories from Odoo", "type": "n8n-nodes-base.odoo", "position": [-1620, -200], "typeVersion": 1, "credentials": {"odooApi": {"id": "tLv6s096EQSwE4vP", "name": "Odoo account"}}}, {"parameters": {"resource": "custom", "customResource": "product.attribute", "operation": "getAll", "options": {}}, "id": "731ee032-a03d-402f-8015-1a569893c84b", "name": "Get Attributes from Odoo", "type": "n8n-nodes-base.odoo", "position": [-1620, 20], "typeVersion": 1, "credentials": {"odooApi": {"id": "tLv6s096EQSwE4vP", "name": "Odoo account"}}}, {"parameters": {"documentId": {"__rl": true, "value": "1BSAa8scQzL7apV--Eg13ZtRLEni6zlNbSVLm_bwSz2A", "mode": "list", "cachedResultName": "Ceratech Categories", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1BSAa8scQzL7apV--Eg13ZtRLEni6zlNbSVLm_bwSz2A/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "categories", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1BSAa8scQzL7apV--Eg13ZtRLEni6zlNbSVLm_bwSz2A/edit#gid=0"}, "options": {}}, "id": "ec1fdd15-c45a-426f-9a83-2c5e0d73fcca", "name": "Read Categories Mapping", "type": "n8n-nodes-base.googleSheets", "position": [-1620, 220], "typeVersion": 4, "credentials": {"googleSheetsOAuth2Api": {"id": "dZ7tkqELmygzeIYp", "name": "Google Sheets account"}}}, {"parameters": {"documentId": "your_reorganization_document_id", "sheetName": "re-organization", "options": {}}, "id": "d0849b49-5c1d-41ae-8289-9a78ef406dd8", "name": "Read Processed Products", "type": "n8n-nodes-base.googleSheets", "position": [-1400, 220], "typeVersion": 4, "credentials": {"googleSheetsOAuth2Api": {"id": "dZ7tkqELmygzeIYp", "name": "Google Sheets account"}}}, {"parameters": {"mode": "combine", "mergeByFields": {"values": [{}]}, "options": {}}, "id": "140e76c7-3b5e-4502-bdce-a90c5b9c45fe", "name": "Merge Products & Categories", "type": "n8n-nodes-base.merge", "position": [-1400, -300], "typeVersion": 2.1}, {"parameters": {"mode": "combine", "mergeByFields": {"values": [{}]}, "options": {}}, "id": "47980af1-8851-4827-b89e-2b71d8f6322c", "name": "Merge Attributes & Mapping", "type": "n8n-nodes-base.merge", "position": [-1400, -100], "typeVersion": 2.1}, {"parameters": {"mode": "combine", "mergeByFields": {"values": [{}]}, "options": {}}, "id": "6b87d612-c48f-4d38-b7fa-8a26fd264709", "name": "Merge All Data", "type": "n8n-nodes-base.merge", "position": [-1180, -200], "typeVersion": 2.1}, {"parameters": {"operation": "create", "documentId": "your_document_id", "options": {}}, "id": "aa5c4077-fb97-41cf-80ee-5ab9dbcb7991", "name": "Create Backup", "type": "n8n-nodes-base.googleSheets", "position": [-1180, 20], "typeVersion": 4, "credentials": {"googleSheetsOAuth2Api": {"id": "dZ7tkqELmygzeIYp", "name": "Google Sheets account"}}}, {"parameters": {"jsCode": "// Extract data from merged inputs\nconst allData = $input.all();\nconst products = allData[0]?.json?.products || [];\nconst categories = allData[0]?.json?.categories || [];\nconst attributes = allData[0]?.json?.attributes || [];\nconst mapping = allData[0]?.json?.mapping || [];\nconst processedProducts = $input.item(1)?.json?.processedProducts || [];\n\n// Create a set of already processed product IDs\nconst processedIds = new Set(processedProducts.map(p => p.id));\n\n// Filter out already processed products\nconst unprocessedProducts = products.filter(product => !processedIds.has(product.id));\n\n// Split products into batches of 10\nconst batchSize = 10;\nconst batches = [];\nfor (let i = 0; i < unprocessedProducts.length; i += batchSize) {\n  const batch = {\n    products: unprocessedProducts.slice(i, i + batchSize),\n    categories: categories,\n    attributes: attributes,\n    mapping: mapping,\n    batchNumber: Math.floor(i / batchSize) + 1,\n    totalBatches: Math.ceil(unprocessedProducts.length / batchSize)\n  };\n  batches.push(batch);\n}\n\nreturn batches.map(batch => ({ json: batch }));"}, "id": "33e3eadc-6dbf-4904-be48-76c146c871cf", "name": "Filter and Batch Products", "type": "n8n-nodes-base.code", "position": [-960, -200], "typeVersion": 2}, {"parameters": {"mode": "runOnceForEachItem", "jsCode": "const batch = $json;\nconst prompt = `You are an expert product data analyst. Analyze the following product data and reorganize it according to the category mappings provided.\n\nTASK: Reorganize product categories, merge potential variants, and fix inconsistencies.\n\nPRODUCTS TO ANALYZE (Batch ${batch.batchNumber}/${batch.totalBatches}):\n${JSON.stringify(batch.products, null, 2)}\n\nAVAILABLE CATEGORIES:\n${JSON.stringify(batch.categories, null, 2)}\n\nCATEGORY MAPPINGS:\n${JSON.stringify(batch.mapping, null, 2)}\n\nATTRIBUTES:\n${JSON.stringify(batch.attributes, null, 2)}\n\nINSTRUCTIONS:\n1. Analyze each product for proper categorization\n2. Identify potential product variants that should be merged\n3. Fix category inconsistencies using the mapping\n4. Ensure data quality and consistency\n5. Return ONLY valid JSON with the reorganized products\n\nRETURN FORMAT:\n{\n  \"reorganized_products\": [\n    {\n      \"id\": \"product_id\",\n      \"name\": \"updated_name\",\n      \"category_id\": \"new_category_id\",\n      \"list_price\": \"price\",\n      \"standard_price\": \"cost\",\n      \"description\": \"description\",\n      \"changes_made\": \"description of changes\",\n      \"merged_variants\": [\"list of merged product ids if any\"]\n    }\n  ]\n}`;\n\nreturn { json: { prompt: prompt, batchData: batch } };"}, "id": "d4420562-e91b-42b2-b59f-d108fdbcf977", "name": "Prepare Gemini Prompt", "type": "n8n-nodes-base.code", "position": [-740, -200], "typeVersion": 2}, {"parameters": {}, "id": "f6658c2e-faea-4ab5-9758-3df2b9d2604d", "name": "Basic LLM Chain", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [-520, -200], "typeVersion": 1.4}, {"parameters": {"modelName": "models/gemini-2.5-pro", "options": {}}, "id": "5db4f4b0-b5b7-449d-abf7-4f38b41f6cd7", "name": "Google Gemini Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [-520, -40], "typeVersion": 1, "credentials": {"googlePalmApi": {"id": "APRdhk8GlK6SwVjG", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {"mode": "runOnceForEachItem", "jsCode": "let responseText = $json.response || $json.text || '';\n\n// Try to extract <PERSON><PERSON><PERSON> from the response\nlet parsedData;\ntry {\n  // First try direct parsing\n  parsedData = JSON.parse(responseText);\n} catch (e) {\n  try {\n    // Try to find <PERSON><PERSON><PERSON> within the response\n    const jsonMatch = responseText.match(/\\{[\\s\\S]*\\}/);\n    if (jsonMatch) {\n      parsedData = JSON.parse(jsonMatch[0]);\n    } else {\n      throw new Error('No JSON found in response');\n    }\n  } catch (e2) {\n    // Fallback: create a basic structure\n    console.log('Failed to parse Gemini response:', responseText);\n    parsedData = {\n      reorganized_products: [],\n      error: 'Failed to parse LLM response',\n      original_response: responseText\n    };\n  }\n}\n\n// Validate the structure\nif (!parsedData.reorganized_products || !Array.isArray(parsedData.reorganized_products)) {\n  parsedData = {\n    reorganized_products: [],\n    error: 'Invalid response structure',\n    original_response: responseText\n  };\n}\n\nreturn { json: { ...parsedData, batchData: $('Prepare Gemini Prompt').item.json.batchData } };"}, "id": "db7f0469-9548-4c38-81a3-ef326c9d0ed8", "name": "Parse Gemini Response", "type": "n8n-nodes-base.code", "position": [-160, -200], "typeVersion": 2}, {"parameters": {"options": {}}, "id": "a7b335a5-638f-47d2-bd60-9c94905e9f45", "name": "Split Products", "type": "n8n-nodes-base.splitInBatches", "position": [60, -200], "typeVersion": 3}, {"parameters": {"documentId": {"__rl": true, "value": "1p223ptpK8dk6UNuHhK4xwXC4BzBC_nycsFQ-IAKDlKc", "mode": "list", "cachedResultName": "re-organization", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1p223ptpK8dk6UNuHhK4xwXC4BzBC_nycsFQ-IAKDlKc/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "product_list", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1p223ptpK8dk6UNuHhK4xwXC4BzBC_nycsFQ-IAKDlKc/edit#gid=0"}, "options": {}}, "id": "502e01d6-846b-4e52-bfb6-30d1f278256b", "name": "Append to Re-organization Sheet", "type": "n8n-nodes-base.googleSheets", "position": [460, -220], "typeVersion": 4, "credentials": {"googleSheetsOAuth2Api": {"id": "dZ7tkqELmygzeIYp", "name": "Google Sheets account"}}}, {"parameters": {"resource": "custom", "customResource": "product.template"}, "id": "de3deeae-3dc9-47b7-b254-48e3184f9637", "name": "Update Products in Odoo", "type": "n8n-nodes-base.odoo", "position": [860, -220], "typeVersion": 1, "credentials": {"odooApi": {"id": "tLv6s096EQSwE4vP", "name": "Odoo account"}}}, {"parameters": {"operation": "create"}, "id": "fdf1e297-3e55-4ba0-a439-1e54c2a7ced3", "name": "Send Notification", "type": "n8n-nodes-base.slack", "position": [1080, -220], "typeVersion": 2, "webhookId": "c192927f-54bd-4174-9521-662d43300850", "credentials": {}}], "connections": {"Schedule Trigger": {"main": [[{"node": "Get Products from Odoo", "type": "main", "index": 0}, {"node": "Get Categories from Odoo", "type": "main", "index": 0}, {"node": "Get Attributes from Odoo", "type": "main", "index": 0}, {"node": "Read Categories Mapping", "type": "main", "index": 0}, {"node": "Read Processed Products", "type": "main", "index": 0}]]}, "Get Products from Odoo": {"main": [[{"node": "Merge Products & Categories", "type": "main", "index": 0}]]}, "Get Categories from Odoo": {"main": [[{"node": "Merge Products & Categories", "type": "main", "index": 1}]]}, "Get Attributes from Odoo": {"main": [[{"node": "Merge Attributes & Mapping", "type": "main", "index": 0}]]}, "Read Categories Mapping": {"main": [[{"node": "Merge Attributes & Mapping", "type": "main", "index": 1}]]}, "Merge Products & Categories": {"main": [[{"node": "Merge All Data", "type": "main", "index": 0}]]}, "Merge Attributes & Mapping": {"main": [[{"node": "Merge All Data", "type": "main", "index": 1}]]}, "Merge All Data": {"main": [[{"node": "Filter and Batch Products", "type": "main", "index": 0}, {"node": "Create Backup", "type": "main", "index": 0}]]}, "Filter and Batch Products": {"main": [[{"node": "Prepare Gemini Prompt", "type": "main", "index": 0}]]}, "Prepare Gemini Prompt": {"main": [[{"node": "Basic LLM Chain", "type": "main", "index": 0}]]}, "Basic LLM Chain": {"main": [[{"node": "Parse Gemini Response", "type": "main", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "Basic LLM Chain", "type": "ai_languageModel", "index": 0}]]}, "Parse Gemini Response": {"main": [[{"node": "Split Products", "type": "main", "index": 0}]]}, "Split Products": {"main": [[{"node": "Append to Re-organization Sheet", "type": "main", "index": 0}]]}, "Append to Re-organization Sheet": {"main": [[{"node": "Update Products in Odoo", "type": "main", "index": 0}]]}, "Update Products in Odoo": {"main": [[{"node": "Send Notification", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"templateCredsSetupCompleted": true, "instanceId": "279040e9cdb85e8ba06a771d7e2f12db781b73ea8768bfce2d2787e09f554bea"}}