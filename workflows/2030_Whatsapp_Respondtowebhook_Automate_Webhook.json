{"id": "yxv7OYbDEnqsqfa9", "meta": {"instanceId": "fb924c73af8f703905bc09c9ee8076f48c17b596ed05b18c0ff86915ef8a7c4a"}, "name": "WhatsApp starter workflow", "tags": [], "nodes": [{"id": "9b385dfe-fa67-4c2c-83df-e3e02c0ff796", "name": "Verify", "type": "n8n-nodes-base.webhook", "position": [700, 180], "webhookId": "793f285b-9da7-4a5e-97ce-f0976c113db5", "parameters": {"path": "1fea1f5f-81c0-48ad-ae13-41e0f8e474ed", "options": {}, "responseMode": "responseNode"}, "typeVersion": 1.1}, {"id": "676efc61-c875-4675-a843-20f98ef1a642", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "position": [920, 180], "parameters": {"options": {}, "respondWith": "text", "responseBody": "={{ $json.query['hub.challenge'] }}"}, "typeVersion": 1}, {"id": "8dd6d094-415c-40d7-ad2b-4ed9f2d23232", "name": "Echo the message back", "type": "n8n-nodes-base.whatsApp", "position": [1140, 540], "parameters": {"textBody": "=Echo back: {{ $json.body.entry[0].changes[0].value.messages[0].text.body }}", "operation": "send", "phoneNumberId": "***************", "additionalFields": {}, "recipientPhoneNumber": "={{ $json.body.entry[0].changes[0].value.messages[0].from }}"}, "credentials": {"whatsAppApi": {"id": "dy22WXWn0Xz4WRby", "name": "WhatsApp account"}}, "typeVersion": 1}, {"id": "cd9e2cfd-9589-4390-95fd-f0bc3960d60c", "name": "Is message?", "type": "n8n-nodes-base.if", "position": [920, 540], "parameters": {"options": {"looseTypeValidation": true}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "loose"}, "combinator": "and", "conditions": [{"id": "8a765e57-8e39-4547-a99a-0458df2b75f4", "operator": {"type": "object", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.body.entry[0].changes[0].value.messages[0] }}", "rightValue": ""}]}}, "typeVersion": 2}, {"id": "********-3c4f-467a-b0e9-bf7e6d42cc18", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [660, 46], "parameters": {"width": 618, "height": 272, "content": "## Verify Webhook\n* Go to your [Meta for Developers App page](https://developers.facebook.com/apps/), navigate to the App settings\n* Add a **production webhook URL** as a new Callback URL\n* *Verify* webhook receives a GET Request and sends back a verification code\n"}, "typeVersion": 1}, {"id": "36ffeb5b-165a-4723-8250-a4feb9123140", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [660, 360], "parameters": {"width": 619, "height": 343, "content": "## Main flow\n* *Respond* webhook receives various POST Requests from Meta regarding WhatsApp messages (user messages + status notifications)\n* Check if the incoming JSON contains user message\n* Echo back the text message to the user. This is a custom message, not a WhatsApp Business template message"}, "typeVersion": 1}, {"id": "aa234bca-c8db-43c6-9aeb-02aef6a084e5", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [240, 260], "parameters": {"color": 3, "width": 405, "height": 177, "content": "## Important!\n### Configure the webhook nodes this way:\n* Make sure that both *Verify* and *Respond* have the same URL\n* *Verify* should have GET HTTP Method\n* *Respond* should have POST HTTP Method"}, "typeVersion": 1}, {"id": "2370b81a-0721-42fd-8893-e3ee02e20278", "name": "Respond", "type": "n8n-nodes-base.webhook", "position": [700, 540], "webhookId": "c4cbc1c4-e1f5-4ea5-bd9a-c5f697493985", "parameters": {"path": "1fea1f5f-81c0-48ad-ae13-41e0f8e474ed", "options": {}, "httpMethod": "POST"}, "typeVersion": 1.1}], "active": true, "pinData": {}, "settings": {"callerPolicy": "workflowsFromSameOwner", "executionOrder": "v1", "saveManualExecutions": true, "saveDataSuccessExecution": "all"}, "versionId": "0d254e91-2ad0-4f38-97d5-fec5057043ea", "connections": {"Verify": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "Respond": {"main": [[{"node": "Is message?", "type": "main", "index": 0}]]}, "Is message?": {"main": [[{"node": "Echo the message back", "type": "main", "index": 0}]]}}}