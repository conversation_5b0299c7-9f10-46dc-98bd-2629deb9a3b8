{"id": "xyLfWaqdIoZmbTfv", "meta": {"instanceId": "660cf2c29eb19fa42319afac3bd2a4a74c6354b7c006403f6cba388968b63f5d", "templateCredsSetupCompleted": true}, "name": "ICP Company Scoring", "tags": [{"id": "a8B9vqj0vNLXcKVQ", "name": "template", "createdAt": "2025-04-04T15:38:37.785Z", "updatedAt": "2025-04-04T15:38:37.785Z"}], "nodes": [{"id": "c155fe86-f253-4a94-bee7-0ca70506a7ec", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-120, 0], "parameters": {}, "typeVersion": 1}, {"id": "58ce6a8a-03e8-499a-8a53-c2efe852ccc6", "name": "Get companies", "type": "n8n-nodes-base.googleSheets", "position": [100, 0], "parameters": {"options": {}, "sheetName": {"__rl": true, "mode": "list", "value": 1729280298, "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1WC_awgb-Ohtb0f4o_OJgRcvunTLuS8kFQgk6l8fkR2Q/edit#gid=1729280298", "cachedResultName": "Company"}, "documentId": {"__rl": true, "mode": "list", "value": "1WC_awgb-Ohtb0f4o_OJgRcvunTLuS8kFQgk6l8fkR2Q", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1WC_awgb-Ohtb0f4o_OJgRcvunTLuS8kFQgk6l8fkR2Q/edit?usp=drivesdk", "cachedResultName": "ICP Score for Template"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "CwpCAR1HwgHZpRtJ", "name": "Google Drive"}}, "typeVersion": 4.5}, {"id": "fcd9a28f-4f22-405b-9f1c-b1f313aee4c8", "name": "Calculate ICP Scoring", "type": "n8n-nodes-base.airtop", "position": [320, 0], "parameters": {"url": "={{ $json['Linkedin_URL_Company'] }}", "prompt": "# LinkedIn Company Analysis Prompt\n\nExtract and analyze the following information from the provided LinkedIn company page. Present the results in a structured JSON format.\n\n## Required Data Points\n\n### 1. Company Identity\n- Full company name (including suffixes like Inc., LLC, etc.)\n- Brand tagline/headline (directly under company name)\n- Global headquarters location \n- Company description (full \"About\" section text)\n- Primary website URL (excluding social media links)\n\n### 2. Company Scale\n- Current employee count (from LinkedIn \"X employees\" metric)\n- Employee range bracket: [0-9], [10-150], [150+]\n\n### 3. Business Classification\nEvaluate the following characteristics based on company description, recent posts, and featured content:\n\n#### Automation Agency Status\n- Boolean (true/false) classification\n- Criteria for \"true\":\n  * Company explicitly offers automation services to clients\n  * Core business model involves developing/implementing automations\n  * Primary revenue from automation consulting/development\n\n#### AI Implementation Level\nClassify as [Low/Medium/High] based on:\n- Low: No evidence of AI/automation/scraping usage\n- Medium: Uses AI/automation tools or mentions them as supplementary capabilities\n- High: Core business involves AI development, automation creation, or data harvesting services\n\n### 4. Technical Sophistication\nEvaluate overall technical capabilities as [Basic/Intermediate/Advanced/Expert] based on:\n- Technology stack mentioned\n- Technical job postings\n- Products/services complexity\n- Engineering team size\n- Technical achievements highlighted\n\n### 5. Investment Profile\nIf available, document:\n- Most recent funding round\n- Total funding amount\n- Key investors\n- Last funding date\nMark as \"Not publicly disclosed\" if information unavailable\n\n### 6. ICP (Ideal Customer Profile) Score\nCalculate composite score using this weighted criteria:\n\n| Category | Criteria | Points |\n|----------|----------|--------|\n| AI Focus | Low | 5 |\n| | Medium | 10 |\n| | High | 25 |\n| Technical Level | Basic | 5 |\n| | Intermediate | 15 |\n| | Advanced | 25 |\n| | Expert | 35 |\n| Employee Count | 0-9 | 5 |\n| | 10-150 | 25 |\n| | 150+ | 30 |\n| Agency Status | Automation Agency | 20 |\n| Geography | US/Europe Based | 10 |\n\n## Output Format\nReturn data in the following JSON structure, with all fields required:\n\n```json\n{\n  \"company_profile\": {\n    \"name\": string,\n    \"tagline\": string,\n    \"location\": {\n      \"city\": string,\n      \"state\": string,\n      \"country\": string\n    },\n    \"overview\": string,\n    \"website\": string\n  },\n  \"scale\": {\n    \"employee_count\": number,\n    \"size_bracket\": string\n  },\n  \"classification\": {\n    \"is_automation_agency\": boolean,\n    \"ai_focus_level\": string,\n    \"technical_tier\": string\n  },\n  \"funding\": {\n    \"latest_round\": string,\n    \"total_raised\": string,\n    \"investors\": [string],\n    \"last_updated\": string\n  },\n  \"icp_scoring\": {\n    \"ai_focus_points\": number,\n    \"technical_points\": number,\n    \"size_points\": number,\n    \"agency_points\": number,\n    \"geography_points\": number,\n    \"total_score\": number\n  }\n}\n", "resource": "extraction", "operation": "query", "sessionMode": "new", "additionalFields": {"outputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"company_profile\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"name\": {\n          \"type\": \"string\",\n          \"description\": \"Full company name including suffixes like Inc., LLC, etc.\"\n        },\n        \"tagline\": {\n          \"type\": \"string\",\n          \"description\": \"Brand tagline or headline directly under company name.\"\n        },\n        \"location\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"city\": {\n              \"type\": \"string\",\n              \"description\": \"City of the company's global headquarters.\"\n            },\n            \"state\": {\n              \"type\": \"string\",\n              \"description\": \"State of the company's global headquarters.\"\n            },\n            \"country\": {\n              \"type\": \"string\",\n              \"description\": \"Country of the company's global headquarters.\"\n            }\n          },\n          \"required\": [\n            \"city\",\n            \"state\",\n            \"country\"\n          ],\n          \"additionalProperties\": false\n        },\n        \"overview\": {\n          \"type\": \"string\",\n          \"description\": \"Full 'About' section text of the company.\"\n        },\n        \"website\": {\n          \"type\": \"string\",\n          \"description\": \"Primary website URL excluding social media links.\"\n        }\n      },\n      \"required\": [\n        \"name\",\n        \"tagline\",\n        \"location\",\n        \"overview\",\n        \"website\"\n      ],\n      \"additionalProperties\": false\n    },\n    \"scale\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"employee_count\": {\n          \"type\": \"integer\",\n          \"description\": \"Current employee count from LinkedIn 'X employees' metric.\"\n        },\n        \"size_bracket\": {\n          \"type\": \"string\",\n          \"description\": \"Employee range bracket.\"\n        }\n      },\n      \"required\": [\n        \"employee_count\",\n        \"size_bracket\"\n      ],\n      \"additionalProperties\": false\n    },\n    \"classification\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"is_automation_agency\": {\n          \"type\": \"boolean\",\n          \"description\": \"Boolean classification if the company is an automation agency.\"\n        },\n        \"ai_focus_level\": {\n          \"type\": \"string\",\n          \"description\": \"AI implementation level based on company description and content.\"\n        },\n        \"technical_tier\": {\n          \"type\": \"string\",\n          \"description\": \"Overall technical capabilities of the company.\"\n        }\n      },\n      \"required\": [\n        \"is_automation_agency\",\n        \"ai_focus_level\",\n        \"technical_tier\"\n      ],\n      \"additionalProperties\": false\n    },\n    \"funding\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"latest_round\": {\n          \"type\": \"string\",\n          \"description\": \"Most recent funding round.\"\n        },\n        \"total_raised\": {\n          \"type\": \"string\",\n          \"description\": \"Total funding amount.\"\n        },\n        \"investors\": {\n          \"type\": \"array\",\n          \"items\": {\n            \"type\": \"string\"\n          },\n          \"description\": \"Key investors.\"\n        },\n        \"last_updated\": {\n          \"type\": \"string\",\n          \"description\": \"Last funding date.\"\n        }\n      },\n      \"required\": [\n        \"latest_round\",\n        \"total_raised\",\n        \"investors\",\n        \"last_updated\"\n      ],\n      \"additionalProperties\": false\n    },\n    \"icp_scoring\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"ai_focus_points\": {\n          \"type\": \"integer\",\n          \"description\": \"Points based on AI focus.\"\n        },\n        \"technical_points\": {\n          \"type\": \"integer\",\n          \"description\": \"Points based on technical level.\"\n        },\n        \"size_points\": {\n          \"type\": \"integer\",\n          \"description\": \"Points based on employee count.\"\n        },\n        \"agency_points\": {\n          \"type\": \"integer\",\n          \"description\": \"Points if the company is an automation agency.\"\n        },\n        \"geography_points\": {\n          \"type\": \"integer\",\n          \"description\": \"Points if the company is US/Europe based.\"\n        },\n        \"total_score\": {\n          \"type\": \"integer\",\n          \"description\": \"Total ICP score.\"\n        }\n      },\n      \"required\": [\n        \"ai_focus_points\",\n        \"technical_points\",\n        \"size_points\",\n        \"agency_points\",\n        \"geography_points\",\n        \"total_score\"\n      ],\n      \"additionalProperties\": false\n    }\n  },\n  \"required\": [\n    \"company_profile\",\n    \"scale\",\n    \"classification\",\n    \"funding\",\n    \"icp_scoring\"\n  ],\n  \"additionalProperties\": false,\n  \"$schema\": \"http://json-schema.org/draft-07/schema#\"\n}\n"}}, "credentials": {"airtopApi": {"id": "byhouJF8RLH5DkmY", "name": "Airtop"}}, "typeVersion": 1}, {"id": "67a5824c-b2b0-432f-b52c-bf5ca719268e", "name": "Format response", "type": "n8n-nodes-base.code", "position": [520, 0], "parameters": {"mode": "runOnceForEachItem", "jsCode": "const row_number = $('Get companies').item.json.row_number\nconst Linkedin_URL_Company = $('Get companies').item.json.Linkedin_URL_Company\nconst icp_scoring = JSON.parse($input.item.json.data.modelResponse).icp_scoring\n\nreturn { json: {\n  row_number,\n  Linkedin_URL_Company,\n  ICP_Score_Company: icp_scoring.total_score\n}};"}, "typeVersion": 2}, {"id": "53be1c3c-c54e-414d-837c-61748a39a61c", "name": "Update row", "type": "n8n-nodes-base.googleSheets", "position": [740, 0], "parameters": {"columns": {"value": {}, "schema": [{"id": "Linkedin_URL_Company", "type": "string", "display": true, "required": false, "displayName": "Linkedin_URL_Company", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "ICP_Score_Company", "type": "string", "display": true, "required": false, "displayName": "ICP_Score_Company", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "meta", "type": "string", "display": true, "removed": false, "required": false, "displayName": "meta", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "data", "type": "string", "display": true, "removed": false, "required": false, "displayName": "data", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "errors", "type": "string", "display": true, "removed": false, "required": false, "displayName": "errors", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "warnings", "type": "string", "display": true, "removed": false, "required": false, "displayName": "warnings", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "parsed", "type": "string", "display": true, "removed": false, "required": false, "displayName": "parsed", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "row_number", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "row_number", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "autoMapInputData", "matchingColumns": ["row_number"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "update", "sheetName": {"__rl": true, "mode": "list", "value": 1729280298, "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1WC_awgb-Ohtb0f4o_OJgRcvunTLuS8kFQgk6l8fkR2Q/edit#gid=1729280298", "cachedResultName": "Company"}, "documentId": {"__rl": true, "mode": "list", "value": "1WC_awgb-Ohtb0f4o_OJgRcvunTLuS8kFQgk6l8fkR2Q", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1WC_awgb-Ohtb0f4o_OJgRcvunTLuS8kFQgk6l8fkR2Q/edit?usp=drivesdk", "cachedResultName": "ICP Score for Template"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "CwpCAR1HwgHZpRtJ", "name": "Google Drive"}}, "typeVersion": 4.5}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "e8045806-b5d6-44be-8553-6de69c1f42f4", "connections": {"Get companies": {"main": [[{"node": "Calculate ICP Scoring", "type": "main", "index": 0}]]}, "Format response": {"main": [[{"node": "Update row", "type": "main", "index": 0}]]}, "Calculate ICP Scoring": {"main": [[{"node": "Format response", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Get companies", "type": "main", "index": 0}]]}}}