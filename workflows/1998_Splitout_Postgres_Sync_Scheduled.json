{"id": "wDD4XugmHIvx3KMT", "meta": {"instanceId": "149cdf730f0c143663259ddc6124c9c26e824d8d2d059973b871074cf4bda531"}, "name": "Synchronize your Google Sheets with Postgres", "tags": [], "nodes": [{"id": "44171bad-84b6-49f8-b538-fb0c2d52db43", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [900, 360], "parameters": {"rule": {"interval": [{"field": "hours"}]}}, "typeVersion": 1.1}, {"id": "1d1558cc-523b-4985-81e2-da49e3d0f4b7", "name": "Compare Datasets", "type": "n8n-nodes-base.compareDatasets", "position": [1820, 380], "parameters": {"options": {}, "resolve": "preferInput1", "mergeByFields": {"values": [{"field1": "first_name", "field2": "first_name"}]}}, "typeVersion": 2.3}, {"id": "b4442fd7-6817-40bb-a76e-851659c836ec", "name": "Split Out Relevant Fields", "type": "n8n-nodes-base.splitOut", "position": [1460, 240], "parameters": {"options": {}, "fieldToSplitOut": "first_name, last_name, town, age"}, "typeVersion": 1}, {"id": "b63899bd-f842-4ead-a590-9bdacdc9b3c0", "name": "Retrieve Sheets Data", "type": "n8n-nodes-base.googleSheets", "position": [1200, 240], "parameters": {"options": {}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1jhUobbdaEuX093J745TsPFMPFbzAIIgx6HnIzdqYqhg/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1jhUobbdaEuX093J745TsPFMPFbzAIIgx6HnIzdqYqhg", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1jhUobbdaEuX093J745TsPFMPFbzAIIgx6HnIzdqYqhg/edit?usp=drivesdk", "cachedResultName": "Testing_Sheet"}}, "typeVersion": 4.2}, {"id": "ae4918fb-07ef-48db-ba25-ea34c5af43af", "name": "Select Rows in Postgres", "type": "n8n-nodes-base.postgres", "position": [1200, 540], "parameters": {"table": {"__rl": true, "mode": "list", "value": "testing", "cachedResultName": "testing"}, "schema": {"__rl": true, "mode": "list", "value": "public"}, "options": {}, "operation": "select", "returnAll": true}, "typeVersion": 2.3}, {"id": "4d08d771-0e80-445e-92db-08197418512d", "name": "Insert Rows", "type": "n8n-nodes-base.postgres", "position": [2300, 260], "parameters": {"table": {"__rl": true, "mode": "list", "value": "testing", "cachedResultName": "testing"}, "schema": {"__rl": true, "mode": "list", "value": "public"}, "columns": {"value": {}, "schema": [{"id": "first_name", "type": "string", "display": true, "required": false, "displayName": "first_name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "last_name", "type": "string", "display": true, "required": false, "displayName": "last_name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "town", "type": "string", "display": true, "required": false, "displayName": "town", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "age", "type": "number", "display": true, "required": false, "displayName": "age", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "autoMapInputData", "matchingColumns": []}, "options": {}}, "typeVersion": 2.3}, {"id": "3fd7baa1-72c7-4587-a557-02eb4dfa92f5", "name": "Update Rows", "type": "n8n-nodes-base.postgres", "position": [2300, 460], "parameters": {"table": {"__rl": true, "mode": "list", "value": "testing", "cachedResultName": "testing"}, "schema": {"__rl": true, "mode": "list", "value": "public"}, "columns": {"value": {"age": "={{ $json.age }}", "town": "={{ $json.town }}", "last_name": "={{ $json.last_name }}", "first_name": "={{ $json.first_name }}"}, "schema": [{"id": "first_name", "type": "string", "display": true, "removed": false, "required": false, "displayName": "first_name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "last_name", "type": "string", "display": true, "removed": false, "required": false, "displayName": "last_name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "town", "type": "string", "display": true, "required": false, "displayName": "town", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "age", "type": "number", "display": true, "required": false, "displayName": "age", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["first_name", "last_name"]}, "options": {}, "operation": "update"}, "typeVersion": 2.3}, {"id": "fc8dbe79-a54d-46fb-8ef7-4bb8b2a402ee", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [360, 260], "parameters": {"width": 485.5994596522446, "height": 350.08576009540855, "content": "## Setup ##\nIn order to make this automation work for you, you need to make a few adjustments:\n\n1. Add your Postgres & Google Sheets Credentials to the respective Nodes\n\n2. Select the Sheet (Google Sheets) and the table (Postgres) you want to sync\n\n3. Update the Insert & Update Queries so that the data is updated in the table you also selected the rows from in the first step"}, "typeVersion": 1}, {"id": "3719112b-1ec7-4402-a366-b1b845819e8d", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [2080, 160], "parameters": {"width": 485.5994596522446, "height": 486.693620858174, "content": "## Updating Your Database \nUsing Insert Rows & Update Rows as Separate Postgres Node's"}, "typeVersion": 1}, {"id": "7742972b-7996-4f9a-9c1d-700737b94eec", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1080, 140], "parameters": {"width": 543.3950930518761, "height": 553.2461684092643, "content": "## Retrieving Data & Spitting Out Fields \nGet the Data you want to compare and split out the relevant fields"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "ac0f0ed3-3f25-4672-a34a-29b5f4402e63", "connections": {"Compare Datasets": {"main": [[{"node": "Insert Rows", "type": "main", "index": 0}], [], [{"node": "Update Rows", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "Retrieve Sheets Data", "type": "main", "index": 0}, {"node": "Select Rows in Postgres", "type": "main", "index": 0}]]}, "Retrieve Sheets Data": {"main": [[{"node": "Split Out Relevant Fields", "type": "main", "index": 0}]]}, "Select Rows in Postgres": {"main": [[{"node": "Compare Datasets", "type": "main", "index": 1}]]}, "Split Out Relevant Fields": {"main": [[{"node": "Compare Datasets", "type": "main", "index": 0}]]}}}