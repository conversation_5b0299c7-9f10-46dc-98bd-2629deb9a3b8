{"id": "zlHbtHIcCZ9enKwg", "meta": {"instanceId": "406f1bca875c48c0fa12bf65a32e67f001617a6df6d6fd6dd72bff9d20014812"}, "name": "v1 helper - Find params with affected expressions", "tags": [], "nodes": [{"id": "b3dd44ca-960f-4689-9545-30a05dc0441e", "name": "When clicking \"Execute Workflow\"", "type": "n8n-nodes-base.manualTrigger", "position": [580, 320], "parameters": {}, "typeVersion": 1}, {"id": "96db239d-05b6-4e1d-b101-e66c2a9708f6", "name": "n8n", "type": "n8n-nodes-base.n8n", "position": [800, 320], "parameters": {"filters": {"activeWorkflows": true}}, "credentials": {"n8nApi": {"id": "hcJ2iZYYgs54eCaT", "name": "n8n account"}}, "typeVersion": 1}, {"id": "b2286f6b-ba37-433c-b22a-95032bc25b6e", "name": "Find params with affected expressions", "type": "n8n-nodes-base.code", "position": [1040, 320], "parameters": {"jsCode": "const AFFECTED_EXTENSIONS = ['beginningOf', 'endOfMonth', 'minus', 'plus'];\n\nconst isExpression = (value) => typeof value === 'string' && value.startsWith('={{');\n\nconst containsAny = (str, substrings) => {\n  for (const substring of substrings) {\n    if (str.includes(substring)) return true;\n  }\n  \n  return false;\n}\n\nconst isAffected = (value) => isExpression(value) && containsAny(value, AFFECTED_EXTENSIONS);\n\nfunction findParamsByTest(target, test) {\n  const parameterNames = [];\n\n  function search(obj) {\n    if (typeof obj === 'object') {\n      for (const key in obj) {\n        const value = obj[key];\n\n        if (test(value)) {\n          parameterNames.push(key);\n        } else if (typeof value === 'object') {\n          search(value);\n        }\n      }\n    }\n  }\n\n  search(target);\n\n  return parameterNames;\n}\n\nreturn $input.all().reduce((allLocations, { json: workflow }) => {\n  const perWorkflow = workflow.nodes.reduce((allLocationsPerWorkflow, node) => {\n    const perNode = findParamsByTest(node.parameters, isAffected).map(\n      (parameterName) => {\n\t\treturn {\n\t\t\tworkflowName: workflow.name,\n\t\t\tnodeName: node.name,\n\t\t\tparameterName,\n        };\n      },\n    );\n\n    return [...allLocationsPerWorkflow, ...perNode];\n  }, []);\n\n  return [...allLocations, ...perWorkflow];\n}, []);"}, "typeVersion": 1}, {"id": "ee189fa0-cf89-4b8d-8351-ed9598f18502", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [600, 92], "parameters": {"width": 548.6551724137931, "height": 191.08045977011497, "content": "## v1 Helper\n\nℹ️ This workflow is to be run **after upgrading to n8n v1**.\n\nThis workflow returns all locations where a node in an active workflow contains a parameter using an **expression extension affected by [v1 changes](https://github.com/n8n-io/n8n/pull/6435)**. For every location, please check that the workflow still behaves as intended."}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {}, "versionId": "da694734-30ae-46b1-8e29-877c95b670ab", "connections": {"n8n": {"main": [[{"node": "Find params with affected expressions", "type": "main", "index": 0}]]}, "When clicking \"Execute Workflow\"": {"main": [[{"node": "n8n", "type": "main", "index": 0}]]}}}