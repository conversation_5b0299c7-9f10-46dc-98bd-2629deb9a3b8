{"id": "b8a4IwiwD9SlgF42", "meta": {"instanceId": "31e69f7f4a77bf465b805824e303232f0227212ae922d12133a0f96ffeab4fef", "templateCredsSetupCompleted": true}, "name": "🔥📈🤖 AI Agent for n8n Creators Leaderboard - Find Popular Workflows", "tags": [], "nodes": [{"id": "fcda047d-b609-4791-b3ae-f359d0c6a071", "name": "stats_aggregate_creators", "type": "n8n-nodes-base.httpRequest", "position": [-1240, 1280], "parameters": {"url": "={{ $json.path }}{{ $json['creators-filename'] }}.json", "options": {}}, "typeVersion": 4.2}, {"id": "fa1f51fd-6019-4d47-b17e-8c5621e6ab4c", "name": "stats_aggregate_workflows", "type": "n8n-nodes-base.httpRequest", "position": [-1240, 1500], "parameters": {"url": "={{ $json.path }}{{ $json['workflows-filename'] }}.json", "options": {}}, "typeVersion": 4.2}, {"id": "34c2d0d3-0474-4a69-b1a5-14c9021865cd", "name": "Global Variables", "type": "n8n-nodes-base.set", "position": [-1660, 1480], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "4bcb91c6-d250-4cb4-8ee1-022df13550e1", "name": "path", "type": "string", "value": "https://raw.githubusercontent.com/teds-tech-talks/n8n-community-leaderboard/refs/heads/main/"}, {"id": "a910a798-0bfe-41b1-a4f1-41390c7f6997", "name": "workflows-filename", "type": "string", "value": "=stats_aggregate_workflows"}, {"id": "e977e816-dc1e-43ce-9393-d6488e6832ca", "name": "creators-filename", "type": "string", "value": "=stats_aggregate_creators"}, {"id": "20efae68-948e-445c-ab89-7dd23149dd50", "name": "chart-filename", "type": "string", "value": "=stats_aggregate_chart"}, {"id": "14233ab4-3fa4-4e26-8032-6ffe26cb601e", "name": "datetime", "type": "string", "value": "={{ $now.format('yyyy-MM-dd') }}"}, {"id": "f63dc683-a430-43ec-9c25-53fa5c0a3ced", "name": "username", "type": "string", "value": "={{ $json.query.username }}"}]}}, "typeVersion": 3.4}, {"id": "7e830263-746f-4909-87aa-5e602d39fc3a", "name": "Parse Workflow Data", "type": "n8n-nodes-base.set", "position": [-880, 1560], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "76f4b20e-519e-4d46-aeac-c6c3f98a69fd", "name": "data", "type": "array", "value": "={{ $json.data }}"}]}}, "typeVersion": 3.4}, {"id": "b112dde6-9194-451f-9c5e-b3f648d215da", "name": "Parse Creators Data", "type": "n8n-nodes-base.set", "position": [-880, 1220], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "76f4b20e-519e-4d46-aeac-c6c3f98a69fd", "name": "data", "type": "array", "value": "={{ $json.data }}"}]}}, "typeVersion": 3.4}, {"id": "877e1988-c85c-49a8-8d56-d3954327c6f6", "name": "Take Top 25 Creators", "type": "n8n-nodes-base.limit", "position": [-260, 1220], "parameters": {"maxItems": 25}, "typeVersion": 1}, {"id": "f05db70e-4362-40a4-bc50-6d0c30ea0cc4", "name": "Aggregate", "type": "n8n-nodes-base.aggregate", "position": [-680, 1920], "parameters": {"options": {}, "aggregate": "aggregateAllItemData"}, "typeVersion": 1}, {"id": "1d223053-d895-4545-a9b2-6eeab6200568", "name": "Filter By Creator Username", "type": "n8n-nodes-base.filter", "position": [-880, 1920], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "21b17fb0-1809-4dc0-b775-cf43a570aa3a", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.username }}", "rightValue": "={{ $('Global Variables').item.json.username }}"}]}}, "typeVersion": 2.2}, {"id": "c25ff9ea-1905-4bf0-ac71-5d81c25466b7", "name": "gpt-4o-mini", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [-1960, 600], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {"temperature": 0.1}}, "credentials": {"openAiApi": {"id": "jEMSvKmtYfzAkhe6", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "b21c51fa-c9b3-4c88-ba7b-fe8a97a951c9", "name": "When Executed by Another Workflow", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [-1980, 1480], "parameters": {"inputSource": "jsonExample", "jsonExample": "{\n \"query\": \n {\n \"username\": \n \"joe\"\n }\n}"}, "typeVersion": 1.1}, {"id": "d26278f5-08d8-4640-82a6-1c3615b6f06b", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [-1980, 240], "webhookId": "c118849f-57c9-40cf-bde6-dddefb9adcf4", "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "00aac33e-20c1-4b99-b2f1-07311f73e1da", "name": "Workflow Tool", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [-1360, 600], "parameters": {"name": "n8n_creator_stats", "workflowId": "={{ $workflow.id }}", "description": "Call this tool to get n8n Creator Stats.", "jsonSchemaExample": "{\n \"username\": \"n8n creator username\"\n}", "specifyInputSchema": true}, "typeVersion": 1}, {"id": "0a00599a-928d-4399-b17e-336201a67480", "name": "creator-summary", "type": "n8n-nodes-base.convertToFile", "position": [-1020, 240], "parameters": {"options": {"fileName": "=creator-summary"}, "operation": "toText", "sourceProperty": "output"}, "typeVersion": 1.1}, {"id": "8e4ae379-749d-44ad-80f8-efc836f2ff55", "name": "Workflow Response", "type": "n8n-nodes-base.set", "position": [-420, 1920], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "eeff1310-2e1c-4ea4-9107-a14b1979f74f", "name": "response", "type": "string", "value": "={{ $json.data }}"}]}}, "typeVersion": 3.4}, {"id": "bc8ea963-a57d-44f1-bcd4-36a1dcb34f0a", "name": "n8n Creator Stats Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [-1620, 240], "parameters": {"text": "={{ $json.chatInput }}", "options": {"systemMessage": "=You are tasked with generating a **comprehensive Markdown report** about a specific n8n community workflow contributor using the provided tools. Your report should not only address the user's query but also provide meaningful insights into the contributor's impact on the n8n community. Follow the structure below:\n\n## Detailed Summary\n- Provide a thorough summary of the contributor's workflows.\n- Highlight unique features, key use cases, and notable technical components for each workflow.\n\n## Workflows\nCreate a well-formatted markdown table with these columns:\n- **Workflow Name**: The name of the workflow. Keep the emojies of they exist.\n- **Description**: A brief overview of its purpose and functionality.\n- **Unique Weekly Visitors**: The number of unique users who visited this workflow weekly.\n- **Unique Monthly Visitors**: The number of unique users who visited this workflow monthly.\n- **Unique Weekly Inserters**: The number of unique users who inserted this workflow weekly.\n- **Unique Monthly Inserters**: The number of unique users who inserted this workflow monthly.\n- **Why It’s Popular**: Explain what makes this workflow stand out (e.g., innovative features, ease of use, specific use cases).\n\n## Community Analysis\n- Analyze why these workflows are popular and valued by the n8n community.\n- Discuss any trends, patterns, or feedback that highlight their significance.\n\n## Additional Insights\n- If available, provide extra information about the contributor's overall impact, such as their engagement in community forums or other notable contributions.\n\n## Formatting Guidelines\n- Use Markdown formatting exclusively (headers, lists, and tables) for clarity and organization.\n- Ensure your response is concise yet comprehensive, structured for easy navigation.\n\n## Error Handling\n- If data is unavailable or incomplete, clearly state this in your response and suggest possible reasons or next steps.\n\n## TOOLS\n\n### n8n_creator_stats \n- Use this tool to retrieve detailed statistics about the n8n creator.\n\n\n \n"}, "promptType": "define"}, "typeVersion": 1.7}, {"id": "0e2507bf-4509-4423-ad23-bee9de2be68e", "name": "Save creator-summary.md", "type": "n8n-nodes-base.readWriteFile", "position": [-820, 240], "parameters": {"options": {"append": true}, "fileName": "=C:\\\\Users\\\\<USER>\\Downloads\\\\{{ $binary.data.fileName }}-{{ $now.format('yyyy-MM-dd-hh-mm-ss') }}.md", "operation": "write"}, "typeVersion": 1}, {"id": "d3d39dad-d743-4c44-ad46-c6edbad4c82b", "name": "Summary Report", "type": "n8n-nodes-base.set", "position": [-1020, 620], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "c44ee9a7-e640-4f5e-acbe-ec559868b74c", "name": "output", "type": "string", "value": "={{ $json.output }}"}]}}, "typeVersion": 3.4}, {"id": "6c07ee44-408f-4d4a-bade-e051d780d022", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-1800, 120], "parameters": {"color": 6, "width": 620, "height": 320, "content": "## AI Agent for n8n Creator Leaderboard Stats\nhttps://github.com/teds-tech-talks/n8n-community-leaderboard"}, "typeVersion": 1}, {"id": "a04eb80b-3cb3-44ad-aef2-c622ea2e33eb", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-1440, 480], "parameters": {"width": 260, "height": 280, "content": "## <PERSON><PERSON> Call for n8n Creators Stats"}, "typeVersion": 1}, {"id": "9b44f6e7-666b-4341-8e04-4cf41a5f986e", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-2060, 480], "parameters": {"color": 5, "width": 300, "height": 460, "content": "## Local or Cloud LLM"}, "typeVersion": 1}, {"id": "68fcc9de-f6d5-461c-ae64-8d8cf6892f7a", "name": "<PERSON><PERSON><PERSON> Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOllama", "disabled": true, "position": [-1960, 780], "parameters": {"options": {}}, "credentials": {"ollamaApi": {"id": "IsSBWGtcJbjRiKqD", "name": "Ollama account localhost"}}, "typeVersion": 1}, {"id": "584dd58a-d97d-45c5-974d-95468a55e359", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-1140, 120], "parameters": {"color": 7, "width": 540, "height": 320, "content": "## Save n8n Creator Report Locally\n(optional for local install)"}, "typeVersion": 1}, {"id": "4ea35ccb-a4f4-481c-9122-6fc980be48d5", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-1140, 480], "parameters": {"color": 4, "width": 320, "height": 340, "content": "## Summary Report Response"}, "typeVersion": 1}, {"id": "d48a28e9-041c-4e25-ac38-0f0519566db5", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-1760, 1360], "parameters": {"width": 300, "height": 320, "content": "## Global Workflow Variables\n\n"}, "typeVersion": 1}, {"id": "cb9b62f1-cdc3-4c2a-ba4b-8dc3baecf7e4", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [-1800, 1120], "parameters": {"color": 3, "width": 780, "height": 640, "content": "## Daily n8n Leaderboard Stats\nhttps://github.com/teds-tech-talks/n8n-community-leaderboard\n\n### n8n Leaderboard\nhttps://teds-tech-talks.github.io/n8n-community-leaderboard/"}, "typeVersion": 1}, {"id": "0f12bc26-875e-4cf0-9b87-7459fdfc73e9", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [-980, 1120], "parameters": {"color": 6, "width": 1120, "height": 300, "content": "## n8n Creators Stats"}, "typeVersion": 1}, {"id": "23abdb9b-3aa3-48a8-987d-c0e0bdcec99f", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [-980, 1460], "parameters": {"color": 4, "width": 1120, "height": 300, "content": "## n8n Workflow Stats"}, "typeVersion": 1}, {"id": "7b7f14b4-cde2-46b1-a37f-4fd136c57a44", "name": "Creators Data", "type": "n8n-nodes-base.set", "position": [-60, 1220], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "02b02023-c5a2-4e22-bcf9-2284c434f5d3", "name": "name", "type": "string", "value": "={{ $json.user.name }}"}, {"id": "4582435b-3c76-45e7-a251-12055efa890a", "name": "username", "type": "string", "value": "={{ $json.user.username }}"}, {"id": "b713a971-ce29-43cf-8f42-c426a38c6582", "name": "bio", "type": "string", "value": "={{ $json.user.bio }}"}, {"id": "19a06510-802e-4bd5-9552-7afa7355ff92", "name": "sum_unique_weekly_inserters", "type": "number", "value": "={{ $json.sum_unique_weekly_inserters }}"}, {"id": "e436533a-5170-47c2-809b-7d79502eb009", "name": "sum_unique_monthly_inserters", "type": "number", "value": "={{ $json.sum_unique_monthly_inserters }}"}, {"id": "198fef5d-86b8-4009-b187-6d3e6566d137", "name": "sum_unique_inserters", "type": "number", "value": "={{ $json.sum_unique_inserters }}"}]}}, "typeVersion": 3.4}, {"id": "f3363202-01ac-4ea1-a015-7c16ac1078af", "name": "Workflows Data", "type": "n8n-nodes-base.set", "position": [-60, 1560], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "3bc3cd11-904d-4315-974d-262c0bd5fea7", "name": "template_url", "type": "string", "value": "={{ $json.template_url }}"}, {"id": "c846c523-f077-40cd-b548-32460124ffb9", "name": "wf_detais.name", "type": "string", "value": "={{ $json.wf_detais.name }}"}, {"id": "f330de47-56fb-4657-8a30-5f5e5cfa76d7", "name": "wf_detais.createdAt", "type": "string", "value": "={{ $json.wf_detais.createdAt }}"}, {"id": "f7ed7e51-a7cf-4f2e-8819-f33115c5ad51", "name": "wf_detais.description", "type": "string", "value": "={{ $json.wf_detais.description }}"}, {"id": "02b02023-c5a2-4e22-bcf9-2284c434f5d3", "name": "name", "type": "string", "value": "={{ $json.user.name }}"}, {"id": "4582435b-3c76-45e7-a251-12055efa890a", "name": "username", "type": "string", "value": "={{ $json.user.username }}"}, {"id": "f952cad3-7e62-46b7-aeb7-a5cbf4d46c0d", "name": "unique_weekly_inserters", "type": "number", "value": "={{ $json.unique_weekly_inserters }}"}, {"id": "6123302b-5bda-48f4-9ef2-71ff52a5f3ba", "name": "unique_monthly_inserters", "type": "number", "value": "={{ $json.unique_monthly_inserters }}"}, {"id": "92dca169-e03f-42ad-8790-ebb55c1a7272", "name": "unique_weekly_visitors", "type": "number", "value": "={{ $json.unique_weekly_visitors }}"}, {"id": "ee640389-d396-4d65-8110-836372a51fb0", "name": "unique_monthly_visitors", "type": "number", "value": "={{ $json.unique_monthly_visitors }}"}, {"id": "9f1c5599-3672-4f4e-9742-d7cc564f6714", "name": "user.avatar", "type": "string", "value": "={{ $json.user.avatar }}"}]}}, "typeVersion": 3.4}, {"id": "3ce82825-f85c-4fd3-9273-5c5540a40dbe", "name": "Merge Creators & Workflows", "type": "n8n-nodes-base.merge", "position": [240, 1560], "parameters": {"mode": "combine", "options": {}, "joinMode": "enrichInput1", "fieldsToMatchString": "username"}, "typeVersion": 3}, {"id": "16c383db-c130-484a-8a6b-b927d4c248e9", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [-980, 1800], "parameters": {"width": 480, "height": 320, "content": "## Filter by n8n Creator Username"}, "typeVersion": 1}, {"id": "7451dc33-8944-47c5-92c3-e70d4ce5d107", "name": "Split Out Creators", "type": "n8n-nodes-base.splitOut", "position": [-680, 1220], "parameters": {"options": {}, "fieldToSplitOut": "data"}, "typeVersion": 1}, {"id": "6fa965e1-1474-4154-b4a2-cabdbbb8e90b", "name": "Split Out Workflows", "type": "n8n-nodes-base.splitOut", "position": [-680, 1560], "parameters": {"options": {}, "fieldToSplitOut": "data"}, "typeVersion": 1}, {"id": "7805fa8b-6287-442d-ba2c-11ddb81ba54f", "name": "Sort By Top Weekly Creator Inserts", "type": "n8n-nodes-base.sort", "position": [-480, 1220], "parameters": {"options": {}, "sortFieldsUi": {"sortField": [{"order": "descending", "fieldName": "sum_unique_weekly_inserters"}]}}, "typeVersion": 1}, {"id": "d1651e0d-04c6-4c09-884e-3fd51e885f3d", "name": "Sort By Top Weekly Workflow Inserts", "type": "n8n-nodes-base.sort", "position": [-480, 1560], "parameters": {"options": {}, "sortFieldsUi": {"sortField": [{"order": "descending", "fieldName": "unique_weekly_inserters"}]}}, "typeVersion": 1}, {"id": "3bcf5f34-80fd-40ec-b88c-8b79b3f1677b", "name": "Take Top 300 Workflows", "type": "n8n-nodes-base.limit", "position": [-260, 1560], "parameters": {"maxItems": 300}, "typeVersion": 1}, {"id": "dc7cf074-17a6-411d-8d59-1cfbd23b7bd2", "name": "Sticky Note10", "type": "n8n-nodes-base.stickyNote", "position": [-2060, 1040], "parameters": {"color": 7, "width": 2510, "height": 1120, "content": "## Workflow for n8n Creators Stats"}, "typeVersion": 1}, {"id": "dacb7e61-7853-47f2-b6fd-3ad611701278", "name": "Sticky Note11", "type": "n8n-nodes-base.stickyNote", "position": [-1340, 1160], "parameters": {"color": 7, "width": 280, "height": 560, "content": "## GET n8n Stats from GitHub repo"}, "typeVersion": 1}, {"id": "a2373c55-9e87-4824-adc8-4d4bbf966544", "name": "Sticky Note12", "type": "n8n-nodes-base.stickyNote", "position": [-560, 0], "parameters": {"color": 2, "width": 1000, "height": 1000, "content": "# n8n Creators Leaderboard Stats Workflow\n\n## Overview\nThis workflow aggregates and processes data from the n8n community to generate detailed statistics about creators and their workflows. It fetches information from JSON files stored on GitHub, merges creator and workflow data, filters the results based on a specified username, and uses an AI agent to output a comprehensive Markdown report.\n\n## Data Retrieval\n- **Creators Data**: \n - An HTTP Request node (\"stats_aggregate_creators\") retrieves a JSON file containing aggregated statistics for workflow creators. \n- **Workflows Data**: \n - A separate HTTP Request node (\"stats_aggregate_workflows\") pulls a JSON file with detailed workflow metrics such as visitor counts and inserter statistics. \n- **Global Variables**: \n - A global variable is set with the GitHub repository base URL housing these JSON files, ensuring that the correct data source is used.\n\n## Data Processing and Merging\n- **Parsing the Data**: \n - The \"Parse Creators Data\" and \"Parse Workflow Data\" nodes extract JSON arrays from the retrieved files for further processing. \n- **Limiting and Sorting**: \n - Nodes like \"Take Top 25 Creators\" and \"Take Top 300 Workflows\" limit the result sets, while nodes such as \"Sort By Top Weekly Creator Inserts\" and \"Sort By Top Weekly Workflow Inserts\" sort the data based on performance metrics. \n- **Merging Records**: \n - Data from creators and workflows is merged by matching the username, enriching the dataset with combined statistics for each creator.\n\n## Filtering and Report Generation\n- **Username Filtering**: \n - A filter node (\"Filter By Creator Username\") allows the workflow to focus on a single creator based on the input username (e.g., \"joe\"). \n- **Generating the Markdown Report**: \n - An AI agent node (\"gpt-4o-mini\") processes the filtered data using a predefined prompt. This prompt instructs the agent to produce a detailed Markdown report that includes: \n - An overall summary of the creator’s workflows \n - A Markdown table listing each workflow along with key metrics (unique weekly/monthly visitors and inserters) and a brief explanation of its popularity \n - Insights into trends or community feedback related to the workflows \n- **Output Conversion and Saving**: \n - The resulting text is converted into a file (using the \"creator-summary\" node) and then saved locally with a filename that includes a timestamp, ensuring easy tracking and retrieval\n"}, "typeVersion": 1}, {"id": "99078ba8-612d-494a-976a-15f2065754ed", "name": "Window Buffer Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [-1640, 600], "parameters": {}, "typeVersion": 1.3}, {"id": "79c67fdc-f56c-4abc-908d-cac11e66790b", "name": "Sticky Note13", "type": "n8n-nodes-base.stickyNote", "position": [-1740, 480], "parameters": {"color": 3, "width": 280, "height": 280, "content": "## Chat History Memory"}, "typeVersion": 1}, {"id": "4be97085-519e-4776-88a1-6d95f97c4aa1", "name": "Sticky Note14", "type": "n8n-nodes-base.stickyNote", "position": [-2580, 20], "parameters": {"width": 480, "height": 980, "content": "# Quick Start Guide for the n8n Creators Leaderboard Workflow\n\n## Prerequisites\n- Ensure your n8n instance is running.\n- Verify that the GitHub base URL and file variables (for creators and workflows) are correctly set in the Global Variables node.\n- Confirm that your OpenAI credentials are configured for the AI Agent node.\n\n## How to Start the Workflow\n- **Activate the Workflow:** \n Ensure the workflow is active in your n8n environment.\n\n- **Trigger via Chat:** \n The workflow is initiated by the Chat Trigger node. Send a chat message such as: \n `show me stats for username [desired_username]` \n This input provides the required username for filtering.\n\n- **Processing & Report Generation:** \n Once triggered, the workflow fetches aggregated creator and workflow data from GitHub, processes and merges the information, and then uses the AI Agent to generate a Markdown report.\n\n- **Output:** \n The final Markdown report is saved locally as a file (with a timestamp), which you can review to see detailed leaderboard statistics and insights for the specified creator.\n\n## Summary\nBy sending a chat message with the appropriate username command, you can quickly trigger this workflow, which will then fetch, process, and generate dynamic statistics about n8n community creators. Enjoy exploring your community’s leaderboard data!\n"}, "typeVersion": 1}, {"id": "db011ff6-359d-4b4a-b5b2-29c15b961f68", "name": "Sticky Note15", "type": "n8n-nodes-base.stickyNote", "position": [-2580, 1040], "parameters": {"width": 480, "height": 940, "content": "# Why Use the n8n Creators Leaderboard Workflow?\n\n## Benefits\nThis workflow provides valuable insights into the n8n community by analyzing and presenting detailed statistics about workflow creators and their contributions. It helps users to:\n\n- **Discover Popular Workflows**: Identify the most widely used workflows based on unique visitors and inserters, both weekly and monthly.\n- **Understand Community Trends**: Gain insights into what types of workflows are resonating with the community, enabling better decision-making for creating or improving workflows.\n- **Recognize Top Contributors**: Highlight the most active and impactful creators, fostering collaboration and inspiration within the community.\n- **Save Time with Automation**: Automates data retrieval, processing, and report generation, eliminating manual effort.\n\n## Key Features\n- **Data Aggregation**: Fetches creator and workflow statistics from GitHub repositories.\n- **Custom Filtering**: Allows filtering by specific usernames to focus on individual contributors.\n- **AI-Powered Reports**: Generates comprehensive Markdown reports with detailed summaries, tables, and community analysis.\n- **Output Flexibility**: Saves reports locally for easy access and future reference.\n\n## Use Cases\n- **For Workflow Creators**: Monitor performance metrics of your workflows to understand their impact and optimize them for better engagement.\n- **For Community Managers**: Recognize top contributors and trends to encourage participation and improve community resources.\n- **For New Users**: Explore popular workflows as a starting point for building your own automations.\n\n"}, "typeVersion": 1}], "active": false, "pinData": {"When chat message received": [{"json": {"action": "sendMessage", "chatInput": "\tshow me stats for username joe", "sessionId": "61fd98239a894d969c0b33060f3f9c44"}}], "When Executed by Another Workflow": [{"json": {"query": {"username": "joe"}}}]}, "settings": {"executionOrder": "v1"}, "versionId": "574ed096-a76c-4cfe-b026-20627f454ddc", "connections": {"Aggregate": {"main": [[{"node": "Workflow Response", "type": "main", "index": 0}]]}, "gpt-4o-mini": {"ai_languageModel": [[{"node": "n8n Creator Stats Agent", "type": "ai_languageModel", "index": 0}]]}, "Creators Data": {"main": [[{"node": "Merge Creators & Workflows", "type": "main", "index": 0}]]}, "Workflow Tool": {"ai_tool": [[{"node": "n8n Creator Stats Agent", "type": "ai_tool", "index": 0}]]}, "Workflows Data": {"main": [[{"node": "Merge Creators & Workflows", "type": "main", "index": 1}]]}, "creator-summary": {"main": [[{"node": "Save creator-summary.md", "type": "main", "index": 0}]]}, "Global Variables": {"main": [[{"node": "stats_aggregate_creators", "type": "main", "index": 0}, {"node": "stats_aggregate_workflows", "type": "main", "index": 0}]]}, "Ollama Chat Model": {"ai_languageModel": [[]]}, "Split Out Creators": {"main": [[{"node": "Sort By Top Weekly Creator Inserts", "type": "main", "index": 0}]]}, "Parse Creators Data": {"main": [[{"node": "Split Out Creators", "type": "main", "index": 0}]]}, "Parse Workflow Data": {"main": [[{"node": "Split Out Workflows", "type": "main", "index": 0}]]}, "Split Out Workflows": {"main": [[{"node": "Sort By Top Weekly Workflow Inserts", "type": "main", "index": 0}]]}, "Take Top 25 Creators": {"main": [[{"node": "Creators Data", "type": "main", "index": 0}]]}, "Window Buffer Memory": {"ai_memory": [[{"node": "n8n Creator Stats Agent", "type": "ai_memory", "index": 0}]]}, "Take Top 300 Workflows": {"main": [[{"node": "Workflows Data", "type": "main", "index": 0}]]}, "n8n Creator Stats Agent": {"main": [[{"node": "Summary Report", "type": "main", "index": 0}, {"node": "creator-summary", "type": "main", "index": 0}]]}, "stats_aggregate_creators": {"main": [[{"node": "Parse Creators Data", "type": "main", "index": 0}]]}, "stats_aggregate_workflows": {"main": [[{"node": "Parse Workflow Data", "type": "main", "index": 0}]]}, "Filter By Creator Username": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}, "Merge Creators & Workflows": {"main": [[{"node": "Filter By Creator Username", "type": "main", "index": 0}]]}, "When chat message received": {"main": [[{"node": "n8n Creator Stats Agent", "type": "main", "index": 0}]]}, "When Executed by Another Workflow": {"main": [[{"node": "Global Variables", "type": "main", "index": 0}]]}, "Sort By Top Weekly Creator Inserts": {"main": [[{"node": "Take Top 25 Creators", "type": "main", "index": 0}]]}, "Sort By Top Weekly Workflow Inserts": {"main": [[{"node": "Take Top 300 Workflows", "type": "main", "index": 0}]]}}}