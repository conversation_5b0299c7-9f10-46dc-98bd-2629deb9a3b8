{"id": "zic2ZEHvxHR4UAYI", "meta": {"instanceId": "fb924c73af8f703905bc09c9ee8076f48c17b596ed05b18c0ff86915ef8a7c4a"}, "name": "Import multiple CSV to GoogleSheet", "tags": [], "nodes": [{"id": "cd5adfcc-5b92-4a75-8e78-c2c1218d946a", "name": "When clicking \"Execute Workflow\"", "type": "n8n-nodes-base.manualTrigger", "position": [920, 380], "parameters": {}, "typeVersion": 1}, {"id": "17305629-bb19-4b55-964e-689ab5f4d557", "name": "Read Binary Files", "type": "n8n-nodes-base.readBinaryFiles", "position": [1120, 380], "parameters": {"fileSelector": "=./.n8n/*.csv"}, "typeVersion": 1}, {"id": "d3055f63-67fa-4dcd-886d-fe6f56fb7058", "name": "Split In Batches", "type": "n8n-nodes-base.splitInBatches", "position": [1320, 380], "parameters": {"options": {}, "batchSize": 1}, "typeVersion": 2}, {"id": "597e9b14-1a8c-4fbb-b5df-c965db1e0e16", "name": "Read CSV", "type": "n8n-nodes-base.spreadsheetFile", "position": [1520, 360], "parameters": {"options": {"rawData": true, "headerRow": true, "readAsString": true, "includeEmptyCells": false}, "fileFormat": "csv"}, "typeVersion": 2}, {"id": "90d5ccac-f2a3-42b6-8fa3-d05450ffa67b", "name": "Remove duplicates", "type": "n8n-nodes-base.itemLists", "position": [1520, 600], "parameters": {"compare": "<PERSON><PERSON><PERSON>s", "options": {}, "operation": "removeDuplicates", "fieldsToCompare": "user_name"}, "typeVersion": 3}, {"id": "2bddcd85-1c99-41ec-8e16-ab75631c3fb9", "name": "Keep only subscribers", "type": "n8n-nodes-base.filter", "position": [1720, 600], "parameters": {"conditions": {"string": [{"value1": "={{ $json.subscribed }}", "value2": "TRUE"}]}}, "typeVersion": 1}, {"id": "4ac13e9d-8523-4ff3-b778-1d9f0dc744e3", "name": "Sort by date", "type": "n8n-nodes-base.itemLists", "position": [1920, 600], "parameters": {"options": {}, "operation": "sort", "sortFieldsUi": {"sortField": [{"fieldName": "date_subscribed"}]}}, "typeVersion": 3}, {"id": "862a7ded-0199-48bb-8183-10f9ae06724b", "name": "Upload to spreadsheet", "type": "n8n-nodes-base.googleSheets", "position": [2120, 600], "parameters": {"columns": {"value": {}, "schema": [{"id": "user_name", "type": "string", "display": true, "removed": false, "required": false, "displayName": "user_name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "user_email", "type": "string", "display": true, "removed": true, "required": false, "displayName": "user_email", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "subscribed", "type": "string", "display": true, "removed": true, "required": false, "displayName": "subscribed", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "date_subscribed", "type": "string", "display": true, "removed": true, "required": false, "displayName": "date_subscribed", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "autoMapInputData", "matchingColumns": ["user_name"]}, "options": {}, "operation": "appendOrUpdate", "sheetName": {"__rl": true, "mode": "list", "value": **********, "cachedResultUrl": "https://docs.google.com/spreadsheets/d/13YYuEJ1cDf-t8P2MSTFWnnNHCreQ6Zo8oPSp7WeNnbY/edit#gid=**********", "cachedResultName": "n8n-sheet"}, "documentId": {"__rl": true, "mode": "url", "value": "https://docs.google.com/spreadsheets/d/13YYuEJ1cDf-t8P2MSTFWnnNHCreQ6Zo8oPSp7WeNnbY"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "54", "name": "Google Sheets account"}}, "typeVersion": 4}, {"id": "95b499b4-024d-49a5-887f-f2f74bd1b9a1", "name": "Assign source file name", "type": "n8n-nodes-base.set", "position": [1720, 360], "parameters": {"fields": {"values": [{"name": "Source", "stringValue": "={{ $('Split In Batches').item.binary.data.fileName }}"}]}, "options": {}}, "typeVersion": 3}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "a6ccb0b8-04bd-407d-b5ca-010c68bb2128", "connections": {"Read CSV": {"main": [[{"node": "Assign source file name", "type": "main", "index": 0}]]}, "Sort by date": {"main": [[{"node": "Upload to spreadsheet", "type": "main", "index": 0}]]}, "Split In Batches": {"main": [[{"node": "Read CSV", "type": "main", "index": 0}], [{"node": "Remove duplicates", "type": "main", "index": 0}]]}, "Read Binary Files": {"main": [[{"node": "Split In Batches", "type": "main", "index": 0}]]}, "Remove duplicates": {"main": [[{"node": "Keep only subscribers", "type": "main", "index": 0}]]}, "Keep only subscribers": {"main": [[{"node": "Sort by date", "type": "main", "index": 0}]]}, "Assign source file name": {"main": [[{"node": "Split In Batches", "type": "main", "index": 0}]]}, "When clicking \"Execute Workflow\"": {"main": [[{"node": "Read Binary Files", "type": "main", "index": 0}]]}}}